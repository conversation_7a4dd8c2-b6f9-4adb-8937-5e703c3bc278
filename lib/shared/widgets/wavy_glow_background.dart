import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:safea/core/theme/colors.dart';

/// Reusable wavy glow background widget
class WavyGlowBackground extends StatefulWidget {
  const WavyGlowBackground({
    required this.child,
    super.key,
    this.primaryColor,
    this.backgroundColor,
    this.intensity = 1.0,
    this.showLogo = false,
    this.logoText,
    this.logoStyle,
  });
  final Widget child;
  final Color? primaryColor;
  final Color? backgroundColor;
  final double intensity;
  final bool showLogo;
  final String? logoText;
  final TextStyle? logoStyle;

  @override
  State<WavyGlowBackground> createState() => _WavyGlowBackgroundState();
}

class _WavyGlowBackgroundState extends State<WavyGlowBackground>
    with TickerProviderStateMixin {
  late AnimationController _waveController;
  late AnimationController _pulseController;
  late Animation<double> _waveAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _waveController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    )..repeat();

    _waveAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(parent: _waveController, curve: Curves.linear));

    _pulseController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);

    _pulseAnimation = Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _waveController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Background glow effect
        Positioned.fill(
          child: AnimatedBuilder(
            animation: Listenable.merge([_waveAnimation, _pulseAnimation]),
            builder: (context, child) {
              return CustomPaint(
                painter: WavyGlowPainter(
                  waveValue: _waveAnimation.value,
                  pulseValue: _pulseAnimation.value,
                  primaryColor: widget.primaryColor ?? AppColors.primary,
                  backgroundColor:
                      widget.backgroundColor ?? AppColors.background,
                  intensity: widget.intensity,
                  showLogo: widget.showLogo,
                  logoText: widget.logoText,
                  logoStyle: widget.logoStyle,
                ),
                size: Size.infinite,
              );
            },
          ),
        ),
        // Child content
        widget.child,
      ],
    );
  }
}

/// Standalone wavy glow effect widget (for smaller components)
class WavyGlowEffect extends StatefulWidget {
  const WavyGlowEffect({
    required this.width,
    required this.height,
    super.key,
    this.primaryColor,
    this.backgroundColor,
    this.showLogo = false,
    this.logoText,
    this.logoStyle,
    this.intensity = 1.0,
  });
  final double width;
  final double height;
  final Color? primaryColor;
  final Color? backgroundColor;
  final bool showLogo;
  final String? logoText;
  final TextStyle? logoStyle;
  final double intensity;

  @override
  State<WavyGlowEffect> createState() => _WavyGlowEffectState();
}

class _WavyGlowEffectState extends State<WavyGlowEffect>
    with TickerProviderStateMixin {
  late AnimationController _waveController;
  late AnimationController _pulseController;
  late Animation<double> _waveAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _waveController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    )..repeat();

    _waveAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(parent: _waveController, curve: Curves.linear));

    _pulseController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);

    _pulseAnimation = Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _waveController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_waveAnimation, _pulseAnimation]),
      builder: (context, child) {
        return CustomPaint(
          painter: WavyGlowPainter(
            waveValue: _waveAnimation.value,
            pulseValue: _pulseAnimation.value,
            primaryColor: widget.primaryColor ?? AppColors.primary,
            backgroundColor: widget.backgroundColor ?? AppColors.background,
            intensity: widget.intensity,
            showLogo: widget.showLogo,
            logoText: widget.logoText,
            logoStyle: widget.logoStyle,
          ),
          child: Container(
            width: widget.width,
            height: widget.height,
            alignment: Alignment.center,
            child: widget.showLogo && widget.logoText != null
                ? Text(widget.logoText!, style: widget.logoStyle)
                : null,
          ),
        );
      },
    );
  }
}

class WavyGlowPainter extends CustomPainter {
  WavyGlowPainter({
    required this.waveValue,
    required this.pulseValue,
    required this.primaryColor,
    required this.backgroundColor,
    this.intensity = 1.0,
    this.showLogo = false,
    this.logoText,
    this.logoStyle,
  });
  final double waveValue;
  final double pulseValue;
  final Color primaryColor;
  final Color backgroundColor;
  final double intensity;
  final bool showLogo;
  final String? logoText;
  final TextStyle? logoStyle;

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);

    // Calculate scale based on size (for background vs component usage)
    final isFullScreen = size.width > 300 || size.height > 400;
    final scale = isFullScreen ? 1.0 : 0.7;

    if (isFullScreen) {
      // Full screen background - multiple large layers
      _drawGlowLayer(
        canvas,
        center,
        size.width * 0.4 * scale,
        size.height * 0.3 * scale,
        0.2 * intensity,
        4,
      );
      _drawGlowLayer(
        canvas,
        center,
        size.width * 0.3 * scale,
        size.height * 0.25 * scale,
        0.3 * intensity,
        3,
      );
      _drawGlowLayer(
        canvas,
        center,
        size.width * 0.2 * scale,
        size.height * 0.15 * scale,
        0.4 * intensity,
        2,
      );
      _drawGlowLayer(
        canvas,
        center,
        size.width * 0.15 * scale,
        size.height * 0.1 * scale,
        0.6 * intensity,
        1,
      );
    } else {
      // Component usage - smaller focused glow
      _drawGlowLayer(
        canvas,
        center,
        size.width * 0.5 * scale,
        size.height * 0.83 * scale,
        0.3 * intensity,
        5,
      );
      _drawGlowLayer(
        canvas,
        center,
        size.width * 0.33 * scale,
        size.height * 0.58 * scale,
        0.5 * intensity,
        3,
      );
      _drawGlowLayer(
        canvas,
        center,
        size.width * 0.2 * scale,
        size.height * 0.33 * scale,
        0.8 * intensity,
        2,
      );

      if (showLogo) {
        _drawCoreOval(
          canvas,
          center,
          size.width * 0.13 * scale,
          size.height * 0.23 * scale,
        );
      }
    }
  }

  void _drawGlowLayer(
    Canvas canvas,
    Offset center,
    double baseWidth,
    double baseHeight,
    double opacity,
    int waveIntensity,
  ) {
    final width = baseWidth * pulseValue;
    final height = baseHeight * pulseValue;

    final gradient = RadialGradient(
      colors: [
        primaryColor.withOpacity(opacity),
        primaryColor.withOpacity(opacity * 0.6),
        primaryColor.withOpacity(opacity * 0.3),
        Colors.transparent,
      ],
      stops: const [0.0, 0.4, 0.7, 1.0],
    );

    final paint = Paint()
      ..shader = gradient.createShader(
        Rect.fromCenter(center: center, width: width * 2, height: height * 2),
      )
      ..style = PaintingStyle.fill;

    final path = Path();
    const steps = 60;

    for (var i = 0; i <= steps; i++) {
      final angle = (i / steps) * 2 * math.pi;
      final waveOffset =
          waveIntensity *
          math.sin(angle * 3 + waveValue) *
          (width * 0.02 * intensity);
      final radiusX = width / 2 + waveOffset;
      final radiusY = height / 2 + waveOffset * 0.5;

      final x = center.dx + radiusX * math.cos(angle);
      final y = center.dy + radiusY * math.sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    canvas.drawPath(path, paint);
  }

  void _drawCoreOval(
    Canvas canvas,
    Offset center,
    double baseWidth,
    double baseHeight,
  ) {
    final width = baseWidth * pulseValue;
    final height = baseHeight * pulseValue;

    final bgPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = primaryColor.withOpacity(0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    final path = Path();
    const steps = 40;

    for (var i = 0; i <= steps; i++) {
      final angle = (i / steps) * 2 * math.pi;
      final waveOffset = 1 * math.sin(angle * 2 + waveValue) * (width * 0.02);
      final radiusX = width / 2 + waveOffset;
      final radiusY = height / 2 + waveOffset * 0.3;

      final x = center.dx + radiusX * math.cos(angle);
      final y = center.dy + radiusY * math.sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    canvas
      ..drawPath(path, bgPaint)
      ..drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
