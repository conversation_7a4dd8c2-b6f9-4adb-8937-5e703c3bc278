import 'package:flutter/material.dart';
import 'package:styled_text/styled_text.dart';
import '../../core/theme/app_text_colors.dart';
import 'text_widget_tags.dart';

/// A reusable text widget that supports styled text with custom tags
///
/// This widget provides a consistent way to display text throughout the app
/// with support for styling through HTML-like tags.
///
/// Example usage:
/// ```dart
/// TextWidget(
///   'Hello <b>World</b>!',
///   size: 16,
///   color: Colors.black,
/// )
///
/// TextWidget(
///   'Click <link>here</link> for more info',
///   customTag: {
///     'link': StyledTextActionTag(
///       (text, attributes) => _handleLinkTap(),
///       style: TextStyle(color: Colors.blue),
///     ),
///   },
/// )
/// ```
class TextWidget extends StatelessWidget {
  /// Creates a TextWidget with styled text support
  const TextWidget(
    this.styledText, {
    super.key,
    this.customTag = const {},
    this.color = AColor.textPrimaryVariant,
    this.fontFamily,
    this.textAlign = TextAlign.start,
    this.maxLines = 999,
    this.fontWeight = FontWeight.normal,
    this.overflow = TextOverflow.ellipsis,
    this.size,
    this.style,
  });

  /// The text content with optional styling tags
  final String styledText;

  /// Custom tags to extend the default tag set
  final Map<String, StyledTextTagBase> customTag;

  /// Default text color
  final Color? color;

  /// Font family for the text
  final String? fontFamily;

  /// Text alignment
  final TextAlign textAlign;

  /// Maximum number of lines
  final int? maxLines;

  /// Font weight
  final FontWeight? fontWeight;

  /// Text overflow behavior
  final TextOverflow overflow;

  /// Font size
  final double? size;

  /// Custom text style (overrides individual properties)
  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    // Get theme colors for dynamic styling
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Create updated tags with theme colors
    final Map<String, StyledTextTagBase> themedTags = {
      ...defaultTextWidgetTags,
      // Override primary color with theme primary
      'primary': StyledTextTag(style: TextStyle(color: colorScheme.primary)),
      // Add theme-aware tags
      'onSurface': StyledTextTag(
        style: TextStyle(color: colorScheme.onSurface),
      ),
      'onPrimary': StyledTextTag(
        style: TextStyle(color: colorScheme.onPrimary),
      ),
      'surface': StyledTextTag(style: TextStyle(color: colorScheme.surface)),
      ...customTag,
    };

    return StyledText(
      text: styledText,
      tags: themedTags,
      overflow: overflow,
      textAlign: textAlign,
      maxLines: maxLines,
      textScaleFactor: 1.0, // Prevent system text scaling
      style:
          style ??
          TextStyle(
            color: color,
            fontWeight: fontWeight,
            fontFamily: fontFamily,
            fontSize: size,
          ),
    );
  }
}

/// Extension to provide quick access to common text styles
extension TextWidgetExtensions on TextWidget {
  /// Creates a heading text widget
  static TextWidget heading(
    String text, {
    Key? key,
    Color? color,
    double? size = 24,
    FontWeight? fontWeight = FontWeight.bold,
    TextAlign textAlign = TextAlign.start,
    Map<String, StyledTextTagBase> customTag = const {},
  }) {
    return TextWidget(
      text,
      key: key,
      color: color,
      size: size,
      fontWeight: fontWeight,
      textAlign: textAlign,
      customTag: customTag,
    );
  }

  /// Creates a body text widget
  static TextWidget body(
    String text, {
    Key? key,
    Color? color,
    double? size = 16,
    FontWeight? fontWeight = FontWeight.normal,
    TextAlign textAlign = TextAlign.start,
    int? maxLines,
    Map<String, StyledTextTagBase> customTag = const {},
  }) {
    return TextWidget(
      text,
      key: key,
      color: color,
      size: size,
      fontWeight: fontWeight,
      textAlign: textAlign,
      maxLines: maxLines,
      customTag: customTag,
    );
  }

  /// Creates a caption text widget
  static TextWidget caption(
    String text, {
    Key? key,
    Color? color = AColor.textMuted,
    double? size = 12,
    FontWeight? fontWeight = FontWeight.normal,
    TextAlign textAlign = TextAlign.start,
    Map<String, StyledTextTagBase> customTag = const {},
  }) {
    return TextWidget(
      text,
      key: key,
      color: color,
      size: size,
      fontWeight: fontWeight,
      textAlign: textAlign,
      customTag: customTag,
    );
  }
}
