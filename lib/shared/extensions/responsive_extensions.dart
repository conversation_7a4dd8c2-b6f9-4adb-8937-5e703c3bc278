import 'package:flutter/material.dart';

/// Responsive design extensions for BuildContext
extension ResponsiveExtensions on BuildContext {
  /// Get screen width
  double get width => MediaQuery.of(this).size.width;

  /// Get screen height
  double get height => MediaQuery.of(this).size.height;

  /// Get screen size
  Size get screenSize => MediaQuery.of(this).size;

  /// Get device pixel ratio
  double get devicePixelRatio => MediaQuery.of(this).devicePixelRatio;

  /// Check if device is mobile (width < 600)
  bool get isMobile => width < 600;

  /// Check if device is tablet (600 <= width < 1024)
  bool get isTablet => width >= 600 && width < 1024;

  /// Check if device is desktop (width >= 1024)
  bool get isDesktop => width >= 1024;

  /// Get responsive padding based on screen size
  EdgeInsets get responsivePadding {
    if (isMobile) {
      return EdgeInsets.symmetric(
        horizontal: width * 0.06, // 6% of screen width
        vertical: height * 0.02, // 2% of screen height
      );
    } else if (isTablet) {
      return EdgeInsets.symmetric(
        horizontal: width * 0.08,
        vertical: height * 0.03,
      );
    } else {
      return EdgeInsets.symmetric(
        horizontal: width * 0.12,
        vertical: height * 0.04,
      );
    }
  }

  /// Get responsive horizontal padding
  EdgeInsets get responsiveHorizontalPadding {
    return EdgeInsets.symmetric(horizontal: responsiveWidth(16));
  }

  EdgeInsets get responsiveHorizontalPadding32 {
    return EdgeInsets.symmetric(horizontal: responsiveWidth(32));
  }

  /// Get responsive vertical padding
  EdgeInsets get responsiveVerticalPadding {
    return EdgeInsets.symmetric(vertical: responsiveHeight(16));
  }

  /// Get responsive width based on percentage
  double responsiveWidth(double pixels) {
    // Base design width (iPhone 14 Pro: 393px)
    const baseWidth = 393.0;
    return (pixels / baseWidth) * width;
  }

  /// Get responsive height based on percentage
  double responsiveHeight(double pixels) {
    // Base design height (iPhone 14 Pro: 852px)
    const baseHeight = 852.0;
    return (pixels / baseHeight) * height;
  }

  /// Get responsive font size
  double responsiveFontSize(double fontSize) {
    // Scale font size based on screen width
    const baseFontScale = 393.0; // iPhone 14 Pro width
    final scale = width / baseFontScale;

    // Clamp the scale to prevent fonts from being too small or too large
    final clampedScale = scale.clamp(0.8, 1.3);

    return fontSize * clampedScale;
  }

  /// Get responsive spacing
  double responsiveSpacing(double spacing) {
    return responsiveHeight(spacing);
  }

  /// Get responsive border radius
  double responsiveBorderRadius(double radius) {
    return responsiveWidth(radius);
  }

  /// Get responsive icon size
  double responsiveIconSize(double size) {
    return responsiveWidth(size);
  }

  /// Get responsive button height
  double get responsiveButtonHeight {
    if (isMobile) {
      return responsiveHeight(48);
    } else if (isTablet) {
      return responsiveHeight(52);
    } else {
      return responsiveHeight(56);
    }
  }

  /// Get responsive app bar height
  double get responsiveAppBarHeight {
    if (isMobile) {
      return responsiveHeight(56);
    } else {
      return responsiveHeight(64);
    }
  }

  /// Get responsive card padding
  EdgeInsets get responsiveCardPadding {
    return EdgeInsets.all(responsiveWidth(16));
  }

  /// Get responsive margin
  EdgeInsets responsiveMargin({
    double? all,
    double? horizontal,
    double? vertical,
    double? top,
    double? bottom,
    double? left,
    double? right,
  }) {
    return EdgeInsets.only(
      top: responsiveHeight(top ?? vertical ?? all ?? 0),
      bottom: responsiveHeight(bottom ?? vertical ?? all ?? 0),
      left: responsiveWidth(left ?? horizontal ?? all ?? 0),
      right: responsiveWidth(right ?? horizontal ?? all ?? 0),
    );
  }

  /// Get responsive padding
  EdgeInsets responsivePaddingOnly({
    double? all,
    double? horizontal,
    double? vertical,
    double? top,
    double? bottom,
    double? left,
    double? right,
  }) {
    return EdgeInsets.only(
      top: responsiveHeight(top ?? vertical ?? all ?? 0),
      bottom: responsiveHeight(bottom ?? vertical ?? all ?? 0),
      left: responsiveWidth(left ?? horizontal ?? all ?? 0),
      right: responsiveWidth(right ?? horizontal ?? all ?? 0),
    );
  }

  /// Get responsive sized box
  Widget responsiveSizedBox({double? width, double? height}) {
    return SizedBox(
      width: width != null ? responsiveWidth(width) : null,
      height: height != null ? responsiveHeight(height) : null,
    );
  }

  /// Get responsive gap (for Flex widgets)
  Widget responsiveGap(double size) {
    return SizedBox(
      width: responsiveWidth(size),
      height: responsiveHeight(size),
    );
  }
}

/// Responsive breakpoints
class ResponsiveBreakpoints {
  static const double mobile = 600;
  static const double tablet = 1024;
  static const double desktop = 1440;
}

/// Responsive layout builder
class ResponsiveBuilder extends StatelessWidget {
  const ResponsiveBuilder({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  @override
  Widget build(BuildContext context) {
    if (context.isDesktop && desktop != null) {
      return desktop!;
    } else if (context.isTablet && tablet != null) {
      return tablet!;
    } else {
      return mobile;
    }
  }
}
