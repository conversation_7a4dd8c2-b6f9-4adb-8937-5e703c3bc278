import 'package:flutter/material.dart';

/// Responsive design utilities for BuildContext
extension ResponsiveExtension on BuildContext {
  /// Get screen width
  double get width => MediaQuery.of(this).size.width;

  /// Get screen height
  double get height => MediaQuery.of(this).size.height;

  /// Get screen size
  Size get screenSize => MediaQuery.of(this).size;

  /// Get device pixel ratio
  double get devicePixelRatio => MediaQuery.of(this).devicePixelRatio;

  /// Get text scale factor
  double get textScaleFactor => MediaQuery.of(this).textScaler.scale(1.0);

  /// Check if device is in landscape mode
  bool get isLandscape =>
      MediaQuery.of(this).orientation == Orientation.landscape;

  /// Check if device is in portrait mode
  bool get isPortrait =>
      MediaQuery.of(this).orientation == Orientation.portrait;

  /// Get safe area padding
  EdgeInsets get padding => MediaQuery.of(this).padding;

  /// Get view insets (keyboard, etc.)
  EdgeInsets get viewInsets => MediaQuery.of(this).viewInsets;

  // ===== RESPONSIVE DIMENSIONS =====

  /// Get responsive width percentage (0.0 to 1.0)
  double widthPercent(double percent) => width * percent;

  /// Get responsive height percentage (0.0 to 1.0)
  double heightPercent(double percent) => height * percent;

  /// Get responsive padding based on screen width
  double get responsivePadding {
    if (width < 360) return 16.0; // Small phones
    if (width < 400) return 20.0; // Medium phones
    if (width < 600) return 24.0; // Large phones
    return 32.0; // Tablets
  }

  /// Get responsive margin based on screen width
  double get responsiveMargin {
    if (width < 360) return 12.0; // Small phones
    if (width < 400) return 16.0; // Medium phones
    if (width < 600) return 20.0; // Large phones
    return 24.0; // Tablets
  }

  /// Get responsive font size multiplier
  double get fontSizeMultiplier {
    if (width < 360) return 0.9; // Small phones
    if (width < 400) return 1.0; // Medium phones
    if (width < 600) return 1.1; // Large phones
    return 1.2; // Tablets
  }

  /// Get responsive spacing
  double get spacing {
    if (width < 360) return 8.0; // Small phones
    if (width < 400) return 12.0; // Medium phones
    if (width < 600) return 16.0; // Large phones
    return 20.0; // Tablets
  }

  /// Get responsive button height
  double get buttonHeight {
    if (width < 360) return 44.0; // Small phones
    if (width < 400) return 48.0; // Medium phones
    if (width < 600) return 52.0; // Large phones
    return 56.0; // Tablets
  }

  /// Get responsive input field height
  double get inputHeight {
    if (width < 360) return 40.0; // Small phones
    if (width < 400) return 44.0; // Medium phones
    if (width < 600) return 48.0; // Large phones
    return 52.0; // Tablets
  }

  /// Get responsive border radius
  double get borderRadius {
    if (width < 360) return 12.0; // Small phones
    if (width < 400) return 14.0; // Medium phones
    if (width < 600) return 16.0; // Large phones
    return 18.0; // Tablets
  }

  // ===== DEVICE TYPE CHECKS =====

  /// Check if device is a small phone (< 360px width)
  bool get isSmallPhone => width < 360;

  /// Check if device is a medium phone (360-400px width)
  bool get isMediumPhone => width >= 360 && width < 400;

  /// Check if device is a large phone (400-600px width)
  bool get isLargePhone => width >= 400 && width < 600;

  /// Check if device is a tablet (>= 600px width)
  bool get isTablet => width >= 600;

  /// Check if device is mobile (< 600px width)
  bool get isMobile => width < 600;

  // ===== RESPONSIVE HELPERS =====

  /// Get responsive value based on device type
  T responsive<T>({required T mobile, T? tablet}) {
    if (isTablet && tablet != null) return tablet;
    return mobile;
  }

  /// Get responsive value with detailed breakpoints
  T responsiveValue<T>({
    required T defaultValue,
    T? small,
    T? medium,
    T? large,
    T? tablet,
  }) {
    if (isTablet && tablet != null) return tablet;
    if (isLargePhone && large != null) return large;
    if (isMediumPhone && medium != null) return medium;
    if (isSmallPhone && small != null) return small;
    return defaultValue;
  }
}
