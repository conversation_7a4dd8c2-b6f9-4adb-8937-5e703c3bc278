import 'package:flutter/material.dart';

/// Simple localization helper (placeholder implementation)
class AppLocalizations {
  String get appName => 'Amora App';
  String get welcome => 'Welcome';
  String get login => 'Login';
  String get logout => 'Logout';
  String get register => 'Register';
  String get email => 'Email';
  String get password => 'Password';
  String get confirmPassword => 'Confirm Password';
  String get forgotPassword => 'Forgot Password?';
  String get save => 'Save';
  String get cancel => 'Cancel';
  String get loading => 'Loading...';
  String get error => 'Error';
  String get success => 'Success';
}

/// Extension to easily access localization from BuildContext
extension LocalizationExtension on BuildContext {
  /// Get the app localizations (placeholder implementation)
  AppLocalizations get l10n => AppLocalizations();

  /// Get the current locale
  Locale get locale => Localizations.localeOf(this);

  /// Check if the current locale is RTL
  bool get isRTL => Directionality.of(this) == TextDirection.rtl;

  /// Get the current language code
  String get languageCode => locale.languageCode;

  /// Get the current country code
  String? get countryCode => locale.countryCode;
}
