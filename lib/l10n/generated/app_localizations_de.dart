// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get appTitle => 'Safea';

  @override
  String get welcomeMessage => 'Willkommen bei Safea';

  @override
  String get loginTitle => 'Anmelden';

  @override
  String get signUpTitle => 'Registrieren';

  @override
  String get emailLabel => 'E-Mail';

  @override
  String get passwordLabel => 'Passwort';

  @override
  String get confirmPasswordLabel => 'Passwort bestätigen';

  @override
  String get loginButton => 'Anmelden';

  @override
  String get signUpButton => 'Registrieren';

  @override
  String get forgotPassword => 'Passwort vergessen?';

  @override
  String get verificationTitle => 'Bestätige dein Konto';

  @override
  String get verificationDescription =>
      'Der Bestätigungscode wurde an deine E-Mail-Adresse gesendet.';

  @override
  String get setupTitle => 'Setup';

  @override
  String get agreementTitle => 'Unsere Vereinbarung';

  @override
  String get confirmAgreement => 'Bestätige unsere Vereinbarung';

  @override
  String get termsAndConditions => 'Unsere AGB\'s';

  @override
  String get trackingTitle => 'Tracking einschalten';

  @override
  String get enableTracking => 'Tracking einschalten';

  @override
  String get availableTimesTitle => 'Verfügbare Zeiten';

  @override
  String get setTime => 'Zeit setzen';

  @override
  String get noTime => 'Keine Zeit';

  @override
  String get dashboardTitle => 'Dashboard';

  @override
  String get noEmergency => 'Kein Notruf';

  @override
  String get currently => 'zurzeit';

  @override
  String get activelyHelping => 'Aktiv geholfen';

  @override
  String get beingTracked => 'wird verfolgt';

  @override
  String helpDistance(String distance) {
    return 'Hilfe leisten - $distance km entfernt';
  }

  @override
  String get settings => 'Einstellungen';

  @override
  String get cancel => 'Abbrechen';

  @override
  String get save => 'Speichern';

  @override
  String get loading => 'Laden...';

  @override
  String get error => 'Fehler';

  @override
  String get retry => 'Wiederholen';
}
