import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_de.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('de')];

  /// The title of the application
  ///
  /// In de, this message translates to:
  /// **'Safea'**
  String get appTitle;

  /// Welcome message for users
  ///
  /// In de, this message translates to:
  /// **'Willkommen bei Safea'**
  String get welcomeMessage;

  /// Login screen title
  ///
  /// In de, this message translates to:
  /// **'Anmelden'**
  String get loginTitle;

  /// Sign up screen title
  ///
  /// In de, this message translates to:
  /// **'Registrieren'**
  String get signUpTitle;

  /// Email input field label
  ///
  /// In de, this message translates to:
  /// **'E-Mail'**
  String get emailLabel;

  /// Password input field label
  ///
  /// In de, this message translates to:
  /// **'Passwort'**
  String get passwordLabel;

  /// Confirm password input field label
  ///
  /// In de, this message translates to:
  /// **'Passwort bestätigen'**
  String get confirmPasswordLabel;

  /// Login button text
  ///
  /// In de, this message translates to:
  /// **'Anmelden'**
  String get loginButton;

  /// Sign up button text
  ///
  /// In de, this message translates to:
  /// **'Registrieren'**
  String get signUpButton;

  /// Forgot password link text
  ///
  /// In de, this message translates to:
  /// **'Passwort vergessen?'**
  String get forgotPassword;

  /// Verification screen title
  ///
  /// In de, this message translates to:
  /// **'Bestätige dein Konto'**
  String get verificationTitle;

  /// Verification screen description
  ///
  /// In de, this message translates to:
  /// **'Der Bestätigungscode wurde an deine E-Mail-Adresse gesendet.'**
  String get verificationDescription;

  /// Setup screen title
  ///
  /// In de, this message translates to:
  /// **'Setup'**
  String get setupTitle;

  /// Agreement screen title
  ///
  /// In de, this message translates to:
  /// **'Unsere Vereinbarung'**
  String get agreementTitle;

  /// Confirm agreement button text
  ///
  /// In de, this message translates to:
  /// **'Bestätige unsere Vereinbarung'**
  String get confirmAgreement;

  /// Terms and conditions link text
  ///
  /// In de, this message translates to:
  /// **'Unsere AGB\'s'**
  String get termsAndConditions;

  /// Tracking setup screen title
  ///
  /// In de, this message translates to:
  /// **'Tracking einschalten'**
  String get trackingTitle;

  /// Enable tracking button text
  ///
  /// In de, this message translates to:
  /// **'Tracking einschalten'**
  String get enableTracking;

  /// Available times screen title
  ///
  /// In de, this message translates to:
  /// **'Verfügbare Zeiten'**
  String get availableTimesTitle;

  /// Set time button text
  ///
  /// In de, this message translates to:
  /// **'Zeit setzen'**
  String get setTime;

  /// No time button text
  ///
  /// In de, this message translates to:
  /// **'Keine Zeit'**
  String get noTime;

  /// Dashboard screen title
  ///
  /// In de, this message translates to:
  /// **'Dashboard'**
  String get dashboardTitle;

  /// No emergency status text
  ///
  /// In de, this message translates to:
  /// **'Kein Notruf'**
  String get noEmergency;

  /// Currently status text
  ///
  /// In de, this message translates to:
  /// **'zurzeit'**
  String get currently;

  /// Actively helping status text
  ///
  /// In de, this message translates to:
  /// **'Aktiv geholfen'**
  String get activelyHelping;

  /// Being tracked status text
  ///
  /// In de, this message translates to:
  /// **'wird verfolgt'**
  String get beingTracked;

  /// Help distance text
  ///
  /// In de, this message translates to:
  /// **'Hilfe leisten - {distance} km entfernt'**
  String helpDistance(String distance);

  /// Settings screen title
  ///
  /// In de, this message translates to:
  /// **'Einstellungen'**
  String get settings;

  /// Cancel button text
  ///
  /// In de, this message translates to:
  /// **'Abbrechen'**
  String get cancel;

  /// Save button text
  ///
  /// In de, this message translates to:
  /// **'Speichern'**
  String get save;

  /// Loading text
  ///
  /// In de, this message translates to:
  /// **'Laden...'**
  String get loading;

  /// Error text
  ///
  /// In de, this message translates to:
  /// **'Fehler'**
  String get error;

  /// Retry button text
  ///
  /// In de, this message translates to:
  /// **'Wiederholen'**
  String get retry;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['de'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'de':
      return AppLocalizationsDe();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
