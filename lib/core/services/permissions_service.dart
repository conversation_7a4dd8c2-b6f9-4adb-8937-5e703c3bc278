import 'package:permission_handler/permission_handler.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'permissions_service.g.dart';

/// Service for handling app permissions
class PermissionsService {
  /// Request location permission
  static Future<bool> requestLocationPermission() async {
    final status = await Permission.location.request();
    return status.isGranted;
  }

  /// Request notification permission
  static Future<bool> requestNotificationPermission() async {
    final status = await Permission.notification.request();
    return status.isGranted;
  }

  /// Request camera permission (for emergency features)
  static Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status.isGranted;
  }

  /// Request microphone permission (for emergency features)
  static Future<bool> requestMicrophonePermission() async {
    final status = await Permission.microphone.request();
    return status.isGranted;
  }

  /// Request phone permission (for emergency calls)
  static Future<bool> requestPhonePermission() async {
    final status = await Permission.phone.request();
    return status.isGranted;
  }

  /// Check if location permission is granted
  static Future<bool> isLocationPermissionGranted() async {
    final status = await Permission.location.status;
    return status.isGranted;
  }

  /// Check if notification permission is granted
  static Future<bool> isNotificationPermissionGranted() async {
    final status = await Permission.notification.status;
    return status.isGranted;
  }

  /// Check if camera permission is granted
  static Future<bool> isCameraPermissionGranted() async {
    final status = await Permission.camera.status;
    return status.isGranted;
  }

  /// Check if microphone permission is granted
  static Future<bool> isMicrophonePermissionGranted() async {
    final status = await Permission.microphone.status;
    return status.isGranted;
  }

  /// Check if phone permission is granted
  static Future<bool> isPhonePermissionGranted() async {
    final status = await Permission.phone.status;
    return status.isGranted;
  }

  /// Request all required permissions for the app
  static Future<Map<Permission, PermissionStatus>>
  requestAllRequiredPermissions() async {
    final permissions = [
      Permission.location,
      Permission.notification,
      Permission.camera,
      Permission.microphone,
      Permission.phone,
    ];

    return await permissions.request();
  }

  /// Check if all required permissions are granted
  static Future<bool> areAllRequiredPermissionsGranted() async {
    final results = await Future.wait([
      isLocationPermissionGranted(),
      isNotificationPermissionGranted(),
      isCameraPermissionGranted(),
      isMicrophonePermissionGranted(),
      isPhonePermissionGranted(),
    ]);

    return results.every((granted) => granted);
  }

  /// Open app settings for manual permission management
  static Future<bool> openSettings() async {
    return await openAppSettings();
  }

  /// Get permission status for a specific permission
  static Future<PermissionStatus> getPermissionStatus(
    Permission permission,
  ) async {
    return await permission.status;
  }

  /// Check if permission is permanently denied
  static Future<bool> isPermissionPermanentlyDenied(
    Permission permission,
  ) async {
    final status = await permission.status;
    return status.isPermanentlyDenied;
  }
}

/// Provider for permissions service
@riverpod
PermissionsService permissionsService(PermissionsServiceRef ref) {
  return PermissionsService();
}

/// Provider to check if all required permissions are granted
@riverpod
Future<bool> allRequiredPermissionsGranted(
  AllRequiredPermissionsGrantedRef ref,
) async {
  return await PermissionsService.areAllRequiredPermissionsGranted();
}
