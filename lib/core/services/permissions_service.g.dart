// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'permissions_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$permissionsServiceHash() =>
    r'bed140dffbe94e6fe582365702839b8db3feff2f';

/// Provider for permissions service
///
/// Copied from [permissionsService].
@ProviderFor(permissionsService)
final permissionsServiceProvider =
    AutoDisposeProvider<PermissionsService>.internal(
      permissionsService,
      name: r'permissionsServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$permissionsServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PermissionsServiceRef = AutoDisposeProviderRef<PermissionsService>;
String _$allRequiredPermissionsGrantedHash() =>
    r'dfe9eccc5f7f5a4657cc47dc8e73c295139bda74';

/// Provider to check if all required permissions are granted
///
/// Copied from [allRequiredPermissionsGranted].
@ProviderFor(allRequiredPermissionsGranted)
final allRequiredPermissionsGrantedProvider =
    AutoDisposeFutureProvider<bool>.internal(
      allRequiredPermissionsGranted,
      name: r'allRequiredPermissionsGrantedProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$allRequiredPermissionsGrantedHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AllRequiredPermissionsGrantedRef = AutoDisposeFutureProviderRef<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
