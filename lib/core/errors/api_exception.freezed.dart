// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'api_exception.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$ApiException {
  String? get message => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? message) badRequest,
    required TResult Function(String? message) unauthorized,
    required TResult Function(String? message) forbidden,
    required TResult Function(String? message) notFound,
    required TResult Function(String? message) internalServerError,
    required TResult Function(String? message) timeout,
    required TResult Function(String? message) noInternetConnection,
    required TResult Function(String? message) requestCancelled,
    required TResult Function(String? message) badCertificate,
    required TResult Function(String? message) unknown,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? message)? badRequest,
    TResult? Function(String? message)? unauthorized,
    TResult? Function(String? message)? forbidden,
    TResult? Function(String? message)? notFound,
    TResult? Function(String? message)? internalServerError,
    TResult? Function(String? message)? timeout,
    TResult? Function(String? message)? noInternetConnection,
    TResult? Function(String? message)? requestCancelled,
    TResult? Function(String? message)? badCertificate,
    TResult? Function(String? message)? unknown,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? message)? badRequest,
    TResult Function(String? message)? unauthorized,
    TResult Function(String? message)? forbidden,
    TResult Function(String? message)? notFound,
    TResult Function(String? message)? internalServerError,
    TResult Function(String? message)? timeout,
    TResult Function(String? message)? noInternetConnection,
    TResult Function(String? message)? requestCancelled,
    TResult Function(String? message)? badCertificate,
    TResult Function(String? message)? unknown,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BadRequestException value) badRequest,
    required TResult Function(UnauthorizedException value) unauthorized,
    required TResult Function(ForbiddenException value) forbidden,
    required TResult Function(NotFoundException value) notFound,
    required TResult Function(InternalServerErrorException value)
    internalServerError,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(NoInternetConnectionException value)
    noInternetConnection,
    required TResult Function(RequestCancelledException value) requestCancelled,
    required TResult Function(BadCertificateException value) badCertificate,
    required TResult Function(UnknownException value) unknown,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BadRequestException value)? badRequest,
    TResult? Function(UnauthorizedException value)? unauthorized,
    TResult? Function(ForbiddenException value)? forbidden,
    TResult? Function(NotFoundException value)? notFound,
    TResult? Function(InternalServerErrorException value)? internalServerError,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(NoInternetConnectionException value)?
    noInternetConnection,
    TResult? Function(RequestCancelledException value)? requestCancelled,
    TResult? Function(BadCertificateException value)? badCertificate,
    TResult? Function(UnknownException value)? unknown,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BadRequestException value)? badRequest,
    TResult Function(UnauthorizedException value)? unauthorized,
    TResult Function(ForbiddenException value)? forbidden,
    TResult Function(NotFoundException value)? notFound,
    TResult Function(InternalServerErrorException value)? internalServerError,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(NoInternetConnectionException value)? noInternetConnection,
    TResult Function(RequestCancelledException value)? requestCancelled,
    TResult Function(BadCertificateException value)? badCertificate,
    TResult Function(UnknownException value)? unknown,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ApiExceptionCopyWith<ApiException> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ApiExceptionCopyWith<$Res> {
  factory $ApiExceptionCopyWith(
    ApiException value,
    $Res Function(ApiException) then,
  ) = _$ApiExceptionCopyWithImpl<$Res, ApiException>;
  @useResult
  $Res call({String? message});
}

/// @nodoc
class _$ApiExceptionCopyWithImpl<$Res, $Val extends ApiException>
    implements $ApiExceptionCopyWith<$Res> {
  _$ApiExceptionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = freezed}) {
    return _then(
      _value.copyWith(
            message: freezed == message
                ? _value.message
                : message // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$BadRequestExceptionImplCopyWith<$Res>
    implements $ApiExceptionCopyWith<$Res> {
  factory _$$BadRequestExceptionImplCopyWith(
    _$BadRequestExceptionImpl value,
    $Res Function(_$BadRequestExceptionImpl) then,
  ) = __$$BadRequestExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? message});
}

/// @nodoc
class __$$BadRequestExceptionImplCopyWithImpl<$Res>
    extends _$ApiExceptionCopyWithImpl<$Res, _$BadRequestExceptionImpl>
    implements _$$BadRequestExceptionImplCopyWith<$Res> {
  __$$BadRequestExceptionImplCopyWithImpl(
    _$BadRequestExceptionImpl _value,
    $Res Function(_$BadRequestExceptionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = freezed}) {
    return _then(
      _$BadRequestExceptionImpl(
        freezed == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$BadRequestExceptionImpl implements BadRequestException {
  const _$BadRequestExceptionImpl([this.message]);

  @override
  final String? message;

  @override
  String toString() {
    return 'ApiException.badRequest(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BadRequestExceptionImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BadRequestExceptionImplCopyWith<_$BadRequestExceptionImpl> get copyWith =>
      __$$BadRequestExceptionImplCopyWithImpl<_$BadRequestExceptionImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? message) badRequest,
    required TResult Function(String? message) unauthorized,
    required TResult Function(String? message) forbidden,
    required TResult Function(String? message) notFound,
    required TResult Function(String? message) internalServerError,
    required TResult Function(String? message) timeout,
    required TResult Function(String? message) noInternetConnection,
    required TResult Function(String? message) requestCancelled,
    required TResult Function(String? message) badCertificate,
    required TResult Function(String? message) unknown,
  }) {
    return badRequest(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? message)? badRequest,
    TResult? Function(String? message)? unauthorized,
    TResult? Function(String? message)? forbidden,
    TResult? Function(String? message)? notFound,
    TResult? Function(String? message)? internalServerError,
    TResult? Function(String? message)? timeout,
    TResult? Function(String? message)? noInternetConnection,
    TResult? Function(String? message)? requestCancelled,
    TResult? Function(String? message)? badCertificate,
    TResult? Function(String? message)? unknown,
  }) {
    return badRequest?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? message)? badRequest,
    TResult Function(String? message)? unauthorized,
    TResult Function(String? message)? forbidden,
    TResult Function(String? message)? notFound,
    TResult Function(String? message)? internalServerError,
    TResult Function(String? message)? timeout,
    TResult Function(String? message)? noInternetConnection,
    TResult Function(String? message)? requestCancelled,
    TResult Function(String? message)? badCertificate,
    TResult Function(String? message)? unknown,
    required TResult orElse(),
  }) {
    if (badRequest != null) {
      return badRequest(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BadRequestException value) badRequest,
    required TResult Function(UnauthorizedException value) unauthorized,
    required TResult Function(ForbiddenException value) forbidden,
    required TResult Function(NotFoundException value) notFound,
    required TResult Function(InternalServerErrorException value)
    internalServerError,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(NoInternetConnectionException value)
    noInternetConnection,
    required TResult Function(RequestCancelledException value) requestCancelled,
    required TResult Function(BadCertificateException value) badCertificate,
    required TResult Function(UnknownException value) unknown,
  }) {
    return badRequest(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BadRequestException value)? badRequest,
    TResult? Function(UnauthorizedException value)? unauthorized,
    TResult? Function(ForbiddenException value)? forbidden,
    TResult? Function(NotFoundException value)? notFound,
    TResult? Function(InternalServerErrorException value)? internalServerError,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(NoInternetConnectionException value)?
    noInternetConnection,
    TResult? Function(RequestCancelledException value)? requestCancelled,
    TResult? Function(BadCertificateException value)? badCertificate,
    TResult? Function(UnknownException value)? unknown,
  }) {
    return badRequest?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BadRequestException value)? badRequest,
    TResult Function(UnauthorizedException value)? unauthorized,
    TResult Function(ForbiddenException value)? forbidden,
    TResult Function(NotFoundException value)? notFound,
    TResult Function(InternalServerErrorException value)? internalServerError,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(NoInternetConnectionException value)? noInternetConnection,
    TResult Function(RequestCancelledException value)? requestCancelled,
    TResult Function(BadCertificateException value)? badCertificate,
    TResult Function(UnknownException value)? unknown,
    required TResult orElse(),
  }) {
    if (badRequest != null) {
      return badRequest(this);
    }
    return orElse();
  }
}

abstract class BadRequestException implements ApiException {
  const factory BadRequestException([final String? message]) =
      _$BadRequestExceptionImpl;

  @override
  String? get message;

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BadRequestExceptionImplCopyWith<_$BadRequestExceptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UnauthorizedExceptionImplCopyWith<$Res>
    implements $ApiExceptionCopyWith<$Res> {
  factory _$$UnauthorizedExceptionImplCopyWith(
    _$UnauthorizedExceptionImpl value,
    $Res Function(_$UnauthorizedExceptionImpl) then,
  ) = __$$UnauthorizedExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? message});
}

/// @nodoc
class __$$UnauthorizedExceptionImplCopyWithImpl<$Res>
    extends _$ApiExceptionCopyWithImpl<$Res, _$UnauthorizedExceptionImpl>
    implements _$$UnauthorizedExceptionImplCopyWith<$Res> {
  __$$UnauthorizedExceptionImplCopyWithImpl(
    _$UnauthorizedExceptionImpl _value,
    $Res Function(_$UnauthorizedExceptionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = freezed}) {
    return _then(
      _$UnauthorizedExceptionImpl(
        freezed == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$UnauthorizedExceptionImpl implements UnauthorizedException {
  const _$UnauthorizedExceptionImpl([this.message]);

  @override
  final String? message;

  @override
  String toString() {
    return 'ApiException.unauthorized(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnauthorizedExceptionImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UnauthorizedExceptionImplCopyWith<_$UnauthorizedExceptionImpl>
  get copyWith =>
      __$$UnauthorizedExceptionImplCopyWithImpl<_$UnauthorizedExceptionImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? message) badRequest,
    required TResult Function(String? message) unauthorized,
    required TResult Function(String? message) forbidden,
    required TResult Function(String? message) notFound,
    required TResult Function(String? message) internalServerError,
    required TResult Function(String? message) timeout,
    required TResult Function(String? message) noInternetConnection,
    required TResult Function(String? message) requestCancelled,
    required TResult Function(String? message) badCertificate,
    required TResult Function(String? message) unknown,
  }) {
    return unauthorized(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? message)? badRequest,
    TResult? Function(String? message)? unauthorized,
    TResult? Function(String? message)? forbidden,
    TResult? Function(String? message)? notFound,
    TResult? Function(String? message)? internalServerError,
    TResult? Function(String? message)? timeout,
    TResult? Function(String? message)? noInternetConnection,
    TResult? Function(String? message)? requestCancelled,
    TResult? Function(String? message)? badCertificate,
    TResult? Function(String? message)? unknown,
  }) {
    return unauthorized?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? message)? badRequest,
    TResult Function(String? message)? unauthorized,
    TResult Function(String? message)? forbidden,
    TResult Function(String? message)? notFound,
    TResult Function(String? message)? internalServerError,
    TResult Function(String? message)? timeout,
    TResult Function(String? message)? noInternetConnection,
    TResult Function(String? message)? requestCancelled,
    TResult Function(String? message)? badCertificate,
    TResult Function(String? message)? unknown,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BadRequestException value) badRequest,
    required TResult Function(UnauthorizedException value) unauthorized,
    required TResult Function(ForbiddenException value) forbidden,
    required TResult Function(NotFoundException value) notFound,
    required TResult Function(InternalServerErrorException value)
    internalServerError,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(NoInternetConnectionException value)
    noInternetConnection,
    required TResult Function(RequestCancelledException value) requestCancelled,
    required TResult Function(BadCertificateException value) badCertificate,
    required TResult Function(UnknownException value) unknown,
  }) {
    return unauthorized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BadRequestException value)? badRequest,
    TResult? Function(UnauthorizedException value)? unauthorized,
    TResult? Function(ForbiddenException value)? forbidden,
    TResult? Function(NotFoundException value)? notFound,
    TResult? Function(InternalServerErrorException value)? internalServerError,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(NoInternetConnectionException value)?
    noInternetConnection,
    TResult? Function(RequestCancelledException value)? requestCancelled,
    TResult? Function(BadCertificateException value)? badCertificate,
    TResult? Function(UnknownException value)? unknown,
  }) {
    return unauthorized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BadRequestException value)? badRequest,
    TResult Function(UnauthorizedException value)? unauthorized,
    TResult Function(ForbiddenException value)? forbidden,
    TResult Function(NotFoundException value)? notFound,
    TResult Function(InternalServerErrorException value)? internalServerError,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(NoInternetConnectionException value)? noInternetConnection,
    TResult Function(RequestCancelledException value)? requestCancelled,
    TResult Function(BadCertificateException value)? badCertificate,
    TResult Function(UnknownException value)? unknown,
    required TResult orElse(),
  }) {
    if (unauthorized != null) {
      return unauthorized(this);
    }
    return orElse();
  }
}

abstract class UnauthorizedException implements ApiException {
  const factory UnauthorizedException([final String? message]) =
      _$UnauthorizedExceptionImpl;

  @override
  String? get message;

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UnauthorizedExceptionImplCopyWith<_$UnauthorizedExceptionImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ForbiddenExceptionImplCopyWith<$Res>
    implements $ApiExceptionCopyWith<$Res> {
  factory _$$ForbiddenExceptionImplCopyWith(
    _$ForbiddenExceptionImpl value,
    $Res Function(_$ForbiddenExceptionImpl) then,
  ) = __$$ForbiddenExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? message});
}

/// @nodoc
class __$$ForbiddenExceptionImplCopyWithImpl<$Res>
    extends _$ApiExceptionCopyWithImpl<$Res, _$ForbiddenExceptionImpl>
    implements _$$ForbiddenExceptionImplCopyWith<$Res> {
  __$$ForbiddenExceptionImplCopyWithImpl(
    _$ForbiddenExceptionImpl _value,
    $Res Function(_$ForbiddenExceptionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = freezed}) {
    return _then(
      _$ForbiddenExceptionImpl(
        freezed == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$ForbiddenExceptionImpl implements ForbiddenException {
  const _$ForbiddenExceptionImpl([this.message]);

  @override
  final String? message;

  @override
  String toString() {
    return 'ApiException.forbidden(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ForbiddenExceptionImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ForbiddenExceptionImplCopyWith<_$ForbiddenExceptionImpl> get copyWith =>
      __$$ForbiddenExceptionImplCopyWithImpl<_$ForbiddenExceptionImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? message) badRequest,
    required TResult Function(String? message) unauthorized,
    required TResult Function(String? message) forbidden,
    required TResult Function(String? message) notFound,
    required TResult Function(String? message) internalServerError,
    required TResult Function(String? message) timeout,
    required TResult Function(String? message) noInternetConnection,
    required TResult Function(String? message) requestCancelled,
    required TResult Function(String? message) badCertificate,
    required TResult Function(String? message) unknown,
  }) {
    return forbidden(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? message)? badRequest,
    TResult? Function(String? message)? unauthorized,
    TResult? Function(String? message)? forbidden,
    TResult? Function(String? message)? notFound,
    TResult? Function(String? message)? internalServerError,
    TResult? Function(String? message)? timeout,
    TResult? Function(String? message)? noInternetConnection,
    TResult? Function(String? message)? requestCancelled,
    TResult? Function(String? message)? badCertificate,
    TResult? Function(String? message)? unknown,
  }) {
    return forbidden?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? message)? badRequest,
    TResult Function(String? message)? unauthorized,
    TResult Function(String? message)? forbidden,
    TResult Function(String? message)? notFound,
    TResult Function(String? message)? internalServerError,
    TResult Function(String? message)? timeout,
    TResult Function(String? message)? noInternetConnection,
    TResult Function(String? message)? requestCancelled,
    TResult Function(String? message)? badCertificate,
    TResult Function(String? message)? unknown,
    required TResult orElse(),
  }) {
    if (forbidden != null) {
      return forbidden(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BadRequestException value) badRequest,
    required TResult Function(UnauthorizedException value) unauthorized,
    required TResult Function(ForbiddenException value) forbidden,
    required TResult Function(NotFoundException value) notFound,
    required TResult Function(InternalServerErrorException value)
    internalServerError,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(NoInternetConnectionException value)
    noInternetConnection,
    required TResult Function(RequestCancelledException value) requestCancelled,
    required TResult Function(BadCertificateException value) badCertificate,
    required TResult Function(UnknownException value) unknown,
  }) {
    return forbidden(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BadRequestException value)? badRequest,
    TResult? Function(UnauthorizedException value)? unauthorized,
    TResult? Function(ForbiddenException value)? forbidden,
    TResult? Function(NotFoundException value)? notFound,
    TResult? Function(InternalServerErrorException value)? internalServerError,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(NoInternetConnectionException value)?
    noInternetConnection,
    TResult? Function(RequestCancelledException value)? requestCancelled,
    TResult? Function(BadCertificateException value)? badCertificate,
    TResult? Function(UnknownException value)? unknown,
  }) {
    return forbidden?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BadRequestException value)? badRequest,
    TResult Function(UnauthorizedException value)? unauthorized,
    TResult Function(ForbiddenException value)? forbidden,
    TResult Function(NotFoundException value)? notFound,
    TResult Function(InternalServerErrorException value)? internalServerError,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(NoInternetConnectionException value)? noInternetConnection,
    TResult Function(RequestCancelledException value)? requestCancelled,
    TResult Function(BadCertificateException value)? badCertificate,
    TResult Function(UnknownException value)? unknown,
    required TResult orElse(),
  }) {
    if (forbidden != null) {
      return forbidden(this);
    }
    return orElse();
  }
}

abstract class ForbiddenException implements ApiException {
  const factory ForbiddenException([final String? message]) =
      _$ForbiddenExceptionImpl;

  @override
  String? get message;

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ForbiddenExceptionImplCopyWith<_$ForbiddenExceptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NotFoundExceptionImplCopyWith<$Res>
    implements $ApiExceptionCopyWith<$Res> {
  factory _$$NotFoundExceptionImplCopyWith(
    _$NotFoundExceptionImpl value,
    $Res Function(_$NotFoundExceptionImpl) then,
  ) = __$$NotFoundExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? message});
}

/// @nodoc
class __$$NotFoundExceptionImplCopyWithImpl<$Res>
    extends _$ApiExceptionCopyWithImpl<$Res, _$NotFoundExceptionImpl>
    implements _$$NotFoundExceptionImplCopyWith<$Res> {
  __$$NotFoundExceptionImplCopyWithImpl(
    _$NotFoundExceptionImpl _value,
    $Res Function(_$NotFoundExceptionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = freezed}) {
    return _then(
      _$NotFoundExceptionImpl(
        freezed == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$NotFoundExceptionImpl implements NotFoundException {
  const _$NotFoundExceptionImpl([this.message]);

  @override
  final String? message;

  @override
  String toString() {
    return 'ApiException.notFound(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotFoundExceptionImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotFoundExceptionImplCopyWith<_$NotFoundExceptionImpl> get copyWith =>
      __$$NotFoundExceptionImplCopyWithImpl<_$NotFoundExceptionImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? message) badRequest,
    required TResult Function(String? message) unauthorized,
    required TResult Function(String? message) forbidden,
    required TResult Function(String? message) notFound,
    required TResult Function(String? message) internalServerError,
    required TResult Function(String? message) timeout,
    required TResult Function(String? message) noInternetConnection,
    required TResult Function(String? message) requestCancelled,
    required TResult Function(String? message) badCertificate,
    required TResult Function(String? message) unknown,
  }) {
    return notFound(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? message)? badRequest,
    TResult? Function(String? message)? unauthorized,
    TResult? Function(String? message)? forbidden,
    TResult? Function(String? message)? notFound,
    TResult? Function(String? message)? internalServerError,
    TResult? Function(String? message)? timeout,
    TResult? Function(String? message)? noInternetConnection,
    TResult? Function(String? message)? requestCancelled,
    TResult? Function(String? message)? badCertificate,
    TResult? Function(String? message)? unknown,
  }) {
    return notFound?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? message)? badRequest,
    TResult Function(String? message)? unauthorized,
    TResult Function(String? message)? forbidden,
    TResult Function(String? message)? notFound,
    TResult Function(String? message)? internalServerError,
    TResult Function(String? message)? timeout,
    TResult Function(String? message)? noInternetConnection,
    TResult Function(String? message)? requestCancelled,
    TResult Function(String? message)? badCertificate,
    TResult Function(String? message)? unknown,
    required TResult orElse(),
  }) {
    if (notFound != null) {
      return notFound(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BadRequestException value) badRequest,
    required TResult Function(UnauthorizedException value) unauthorized,
    required TResult Function(ForbiddenException value) forbidden,
    required TResult Function(NotFoundException value) notFound,
    required TResult Function(InternalServerErrorException value)
    internalServerError,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(NoInternetConnectionException value)
    noInternetConnection,
    required TResult Function(RequestCancelledException value) requestCancelled,
    required TResult Function(BadCertificateException value) badCertificate,
    required TResult Function(UnknownException value) unknown,
  }) {
    return notFound(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BadRequestException value)? badRequest,
    TResult? Function(UnauthorizedException value)? unauthorized,
    TResult? Function(ForbiddenException value)? forbidden,
    TResult? Function(NotFoundException value)? notFound,
    TResult? Function(InternalServerErrorException value)? internalServerError,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(NoInternetConnectionException value)?
    noInternetConnection,
    TResult? Function(RequestCancelledException value)? requestCancelled,
    TResult? Function(BadCertificateException value)? badCertificate,
    TResult? Function(UnknownException value)? unknown,
  }) {
    return notFound?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BadRequestException value)? badRequest,
    TResult Function(UnauthorizedException value)? unauthorized,
    TResult Function(ForbiddenException value)? forbidden,
    TResult Function(NotFoundException value)? notFound,
    TResult Function(InternalServerErrorException value)? internalServerError,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(NoInternetConnectionException value)? noInternetConnection,
    TResult Function(RequestCancelledException value)? requestCancelled,
    TResult Function(BadCertificateException value)? badCertificate,
    TResult Function(UnknownException value)? unknown,
    required TResult orElse(),
  }) {
    if (notFound != null) {
      return notFound(this);
    }
    return orElse();
  }
}

abstract class NotFoundException implements ApiException {
  const factory NotFoundException([final String? message]) =
      _$NotFoundExceptionImpl;

  @override
  String? get message;

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotFoundExceptionImplCopyWith<_$NotFoundExceptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InternalServerErrorExceptionImplCopyWith<$Res>
    implements $ApiExceptionCopyWith<$Res> {
  factory _$$InternalServerErrorExceptionImplCopyWith(
    _$InternalServerErrorExceptionImpl value,
    $Res Function(_$InternalServerErrorExceptionImpl) then,
  ) = __$$InternalServerErrorExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? message});
}

/// @nodoc
class __$$InternalServerErrorExceptionImplCopyWithImpl<$Res>
    extends _$ApiExceptionCopyWithImpl<$Res, _$InternalServerErrorExceptionImpl>
    implements _$$InternalServerErrorExceptionImplCopyWith<$Res> {
  __$$InternalServerErrorExceptionImplCopyWithImpl(
    _$InternalServerErrorExceptionImpl _value,
    $Res Function(_$InternalServerErrorExceptionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = freezed}) {
    return _then(
      _$InternalServerErrorExceptionImpl(
        freezed == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$InternalServerErrorExceptionImpl
    implements InternalServerErrorException {
  const _$InternalServerErrorExceptionImpl([this.message]);

  @override
  final String? message;

  @override
  String toString() {
    return 'ApiException.internalServerError(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InternalServerErrorExceptionImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InternalServerErrorExceptionImplCopyWith<
    _$InternalServerErrorExceptionImpl
  >
  get copyWith =>
      __$$InternalServerErrorExceptionImplCopyWithImpl<
        _$InternalServerErrorExceptionImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? message) badRequest,
    required TResult Function(String? message) unauthorized,
    required TResult Function(String? message) forbidden,
    required TResult Function(String? message) notFound,
    required TResult Function(String? message) internalServerError,
    required TResult Function(String? message) timeout,
    required TResult Function(String? message) noInternetConnection,
    required TResult Function(String? message) requestCancelled,
    required TResult Function(String? message) badCertificate,
    required TResult Function(String? message) unknown,
  }) {
    return internalServerError(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? message)? badRequest,
    TResult? Function(String? message)? unauthorized,
    TResult? Function(String? message)? forbidden,
    TResult? Function(String? message)? notFound,
    TResult? Function(String? message)? internalServerError,
    TResult? Function(String? message)? timeout,
    TResult? Function(String? message)? noInternetConnection,
    TResult? Function(String? message)? requestCancelled,
    TResult? Function(String? message)? badCertificate,
    TResult? Function(String? message)? unknown,
  }) {
    return internalServerError?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? message)? badRequest,
    TResult Function(String? message)? unauthorized,
    TResult Function(String? message)? forbidden,
    TResult Function(String? message)? notFound,
    TResult Function(String? message)? internalServerError,
    TResult Function(String? message)? timeout,
    TResult Function(String? message)? noInternetConnection,
    TResult Function(String? message)? requestCancelled,
    TResult Function(String? message)? badCertificate,
    TResult Function(String? message)? unknown,
    required TResult orElse(),
  }) {
    if (internalServerError != null) {
      return internalServerError(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BadRequestException value) badRequest,
    required TResult Function(UnauthorizedException value) unauthorized,
    required TResult Function(ForbiddenException value) forbidden,
    required TResult Function(NotFoundException value) notFound,
    required TResult Function(InternalServerErrorException value)
    internalServerError,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(NoInternetConnectionException value)
    noInternetConnection,
    required TResult Function(RequestCancelledException value) requestCancelled,
    required TResult Function(BadCertificateException value) badCertificate,
    required TResult Function(UnknownException value) unknown,
  }) {
    return internalServerError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BadRequestException value)? badRequest,
    TResult? Function(UnauthorizedException value)? unauthorized,
    TResult? Function(ForbiddenException value)? forbidden,
    TResult? Function(NotFoundException value)? notFound,
    TResult? Function(InternalServerErrorException value)? internalServerError,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(NoInternetConnectionException value)?
    noInternetConnection,
    TResult? Function(RequestCancelledException value)? requestCancelled,
    TResult? Function(BadCertificateException value)? badCertificate,
    TResult? Function(UnknownException value)? unknown,
  }) {
    return internalServerError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BadRequestException value)? badRequest,
    TResult Function(UnauthorizedException value)? unauthorized,
    TResult Function(ForbiddenException value)? forbidden,
    TResult Function(NotFoundException value)? notFound,
    TResult Function(InternalServerErrorException value)? internalServerError,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(NoInternetConnectionException value)? noInternetConnection,
    TResult Function(RequestCancelledException value)? requestCancelled,
    TResult Function(BadCertificateException value)? badCertificate,
    TResult Function(UnknownException value)? unknown,
    required TResult orElse(),
  }) {
    if (internalServerError != null) {
      return internalServerError(this);
    }
    return orElse();
  }
}

abstract class InternalServerErrorException implements ApiException {
  const factory InternalServerErrorException([final String? message]) =
      _$InternalServerErrorExceptionImpl;

  @override
  String? get message;

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InternalServerErrorExceptionImplCopyWith<
    _$InternalServerErrorExceptionImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TimeoutExceptionImplCopyWith<$Res>
    implements $ApiExceptionCopyWith<$Res> {
  factory _$$TimeoutExceptionImplCopyWith(
    _$TimeoutExceptionImpl value,
    $Res Function(_$TimeoutExceptionImpl) then,
  ) = __$$TimeoutExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? message});
}

/// @nodoc
class __$$TimeoutExceptionImplCopyWithImpl<$Res>
    extends _$ApiExceptionCopyWithImpl<$Res, _$TimeoutExceptionImpl>
    implements _$$TimeoutExceptionImplCopyWith<$Res> {
  __$$TimeoutExceptionImplCopyWithImpl(
    _$TimeoutExceptionImpl _value,
    $Res Function(_$TimeoutExceptionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = freezed}) {
    return _then(
      _$TimeoutExceptionImpl(
        freezed == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$TimeoutExceptionImpl implements TimeoutException {
  const _$TimeoutExceptionImpl([this.message]);

  @override
  final String? message;

  @override
  String toString() {
    return 'ApiException.timeout(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimeoutExceptionImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TimeoutExceptionImplCopyWith<_$TimeoutExceptionImpl> get copyWith =>
      __$$TimeoutExceptionImplCopyWithImpl<_$TimeoutExceptionImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? message) badRequest,
    required TResult Function(String? message) unauthorized,
    required TResult Function(String? message) forbidden,
    required TResult Function(String? message) notFound,
    required TResult Function(String? message) internalServerError,
    required TResult Function(String? message) timeout,
    required TResult Function(String? message) noInternetConnection,
    required TResult Function(String? message) requestCancelled,
    required TResult Function(String? message) badCertificate,
    required TResult Function(String? message) unknown,
  }) {
    return timeout(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? message)? badRequest,
    TResult? Function(String? message)? unauthorized,
    TResult? Function(String? message)? forbidden,
    TResult? Function(String? message)? notFound,
    TResult? Function(String? message)? internalServerError,
    TResult? Function(String? message)? timeout,
    TResult? Function(String? message)? noInternetConnection,
    TResult? Function(String? message)? requestCancelled,
    TResult? Function(String? message)? badCertificate,
    TResult? Function(String? message)? unknown,
  }) {
    return timeout?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? message)? badRequest,
    TResult Function(String? message)? unauthorized,
    TResult Function(String? message)? forbidden,
    TResult Function(String? message)? notFound,
    TResult Function(String? message)? internalServerError,
    TResult Function(String? message)? timeout,
    TResult Function(String? message)? noInternetConnection,
    TResult Function(String? message)? requestCancelled,
    TResult Function(String? message)? badCertificate,
    TResult Function(String? message)? unknown,
    required TResult orElse(),
  }) {
    if (timeout != null) {
      return timeout(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BadRequestException value) badRequest,
    required TResult Function(UnauthorizedException value) unauthorized,
    required TResult Function(ForbiddenException value) forbidden,
    required TResult Function(NotFoundException value) notFound,
    required TResult Function(InternalServerErrorException value)
    internalServerError,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(NoInternetConnectionException value)
    noInternetConnection,
    required TResult Function(RequestCancelledException value) requestCancelled,
    required TResult Function(BadCertificateException value) badCertificate,
    required TResult Function(UnknownException value) unknown,
  }) {
    return timeout(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BadRequestException value)? badRequest,
    TResult? Function(UnauthorizedException value)? unauthorized,
    TResult? Function(ForbiddenException value)? forbidden,
    TResult? Function(NotFoundException value)? notFound,
    TResult? Function(InternalServerErrorException value)? internalServerError,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(NoInternetConnectionException value)?
    noInternetConnection,
    TResult? Function(RequestCancelledException value)? requestCancelled,
    TResult? Function(BadCertificateException value)? badCertificate,
    TResult? Function(UnknownException value)? unknown,
  }) {
    return timeout?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BadRequestException value)? badRequest,
    TResult Function(UnauthorizedException value)? unauthorized,
    TResult Function(ForbiddenException value)? forbidden,
    TResult Function(NotFoundException value)? notFound,
    TResult Function(InternalServerErrorException value)? internalServerError,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(NoInternetConnectionException value)? noInternetConnection,
    TResult Function(RequestCancelledException value)? requestCancelled,
    TResult Function(BadCertificateException value)? badCertificate,
    TResult Function(UnknownException value)? unknown,
    required TResult orElse(),
  }) {
    if (timeout != null) {
      return timeout(this);
    }
    return orElse();
  }
}

abstract class TimeoutException implements ApiException {
  const factory TimeoutException([final String? message]) =
      _$TimeoutExceptionImpl;

  @override
  String? get message;

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TimeoutExceptionImplCopyWith<_$TimeoutExceptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NoInternetConnectionExceptionImplCopyWith<$Res>
    implements $ApiExceptionCopyWith<$Res> {
  factory _$$NoInternetConnectionExceptionImplCopyWith(
    _$NoInternetConnectionExceptionImpl value,
    $Res Function(_$NoInternetConnectionExceptionImpl) then,
  ) = __$$NoInternetConnectionExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? message});
}

/// @nodoc
class __$$NoInternetConnectionExceptionImplCopyWithImpl<$Res>
    extends
        _$ApiExceptionCopyWithImpl<$Res, _$NoInternetConnectionExceptionImpl>
    implements _$$NoInternetConnectionExceptionImplCopyWith<$Res> {
  __$$NoInternetConnectionExceptionImplCopyWithImpl(
    _$NoInternetConnectionExceptionImpl _value,
    $Res Function(_$NoInternetConnectionExceptionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = freezed}) {
    return _then(
      _$NoInternetConnectionExceptionImpl(
        freezed == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$NoInternetConnectionExceptionImpl
    implements NoInternetConnectionException {
  const _$NoInternetConnectionExceptionImpl([this.message]);

  @override
  final String? message;

  @override
  String toString() {
    return 'ApiException.noInternetConnection(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NoInternetConnectionExceptionImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NoInternetConnectionExceptionImplCopyWith<
    _$NoInternetConnectionExceptionImpl
  >
  get copyWith =>
      __$$NoInternetConnectionExceptionImplCopyWithImpl<
        _$NoInternetConnectionExceptionImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? message) badRequest,
    required TResult Function(String? message) unauthorized,
    required TResult Function(String? message) forbidden,
    required TResult Function(String? message) notFound,
    required TResult Function(String? message) internalServerError,
    required TResult Function(String? message) timeout,
    required TResult Function(String? message) noInternetConnection,
    required TResult Function(String? message) requestCancelled,
    required TResult Function(String? message) badCertificate,
    required TResult Function(String? message) unknown,
  }) {
    return noInternetConnection(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? message)? badRequest,
    TResult? Function(String? message)? unauthorized,
    TResult? Function(String? message)? forbidden,
    TResult? Function(String? message)? notFound,
    TResult? Function(String? message)? internalServerError,
    TResult? Function(String? message)? timeout,
    TResult? Function(String? message)? noInternetConnection,
    TResult? Function(String? message)? requestCancelled,
    TResult? Function(String? message)? badCertificate,
    TResult? Function(String? message)? unknown,
  }) {
    return noInternetConnection?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? message)? badRequest,
    TResult Function(String? message)? unauthorized,
    TResult Function(String? message)? forbidden,
    TResult Function(String? message)? notFound,
    TResult Function(String? message)? internalServerError,
    TResult Function(String? message)? timeout,
    TResult Function(String? message)? noInternetConnection,
    TResult Function(String? message)? requestCancelled,
    TResult Function(String? message)? badCertificate,
    TResult Function(String? message)? unknown,
    required TResult orElse(),
  }) {
    if (noInternetConnection != null) {
      return noInternetConnection(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BadRequestException value) badRequest,
    required TResult Function(UnauthorizedException value) unauthorized,
    required TResult Function(ForbiddenException value) forbidden,
    required TResult Function(NotFoundException value) notFound,
    required TResult Function(InternalServerErrorException value)
    internalServerError,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(NoInternetConnectionException value)
    noInternetConnection,
    required TResult Function(RequestCancelledException value) requestCancelled,
    required TResult Function(BadCertificateException value) badCertificate,
    required TResult Function(UnknownException value) unknown,
  }) {
    return noInternetConnection(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BadRequestException value)? badRequest,
    TResult? Function(UnauthorizedException value)? unauthorized,
    TResult? Function(ForbiddenException value)? forbidden,
    TResult? Function(NotFoundException value)? notFound,
    TResult? Function(InternalServerErrorException value)? internalServerError,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(NoInternetConnectionException value)?
    noInternetConnection,
    TResult? Function(RequestCancelledException value)? requestCancelled,
    TResult? Function(BadCertificateException value)? badCertificate,
    TResult? Function(UnknownException value)? unknown,
  }) {
    return noInternetConnection?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BadRequestException value)? badRequest,
    TResult Function(UnauthorizedException value)? unauthorized,
    TResult Function(ForbiddenException value)? forbidden,
    TResult Function(NotFoundException value)? notFound,
    TResult Function(InternalServerErrorException value)? internalServerError,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(NoInternetConnectionException value)? noInternetConnection,
    TResult Function(RequestCancelledException value)? requestCancelled,
    TResult Function(BadCertificateException value)? badCertificate,
    TResult Function(UnknownException value)? unknown,
    required TResult orElse(),
  }) {
    if (noInternetConnection != null) {
      return noInternetConnection(this);
    }
    return orElse();
  }
}

abstract class NoInternetConnectionException implements ApiException {
  const factory NoInternetConnectionException([final String? message]) =
      _$NoInternetConnectionExceptionImpl;

  @override
  String? get message;

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NoInternetConnectionExceptionImplCopyWith<
    _$NoInternetConnectionExceptionImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RequestCancelledExceptionImplCopyWith<$Res>
    implements $ApiExceptionCopyWith<$Res> {
  factory _$$RequestCancelledExceptionImplCopyWith(
    _$RequestCancelledExceptionImpl value,
    $Res Function(_$RequestCancelledExceptionImpl) then,
  ) = __$$RequestCancelledExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? message});
}

/// @nodoc
class __$$RequestCancelledExceptionImplCopyWithImpl<$Res>
    extends _$ApiExceptionCopyWithImpl<$Res, _$RequestCancelledExceptionImpl>
    implements _$$RequestCancelledExceptionImplCopyWith<$Res> {
  __$$RequestCancelledExceptionImplCopyWithImpl(
    _$RequestCancelledExceptionImpl _value,
    $Res Function(_$RequestCancelledExceptionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = freezed}) {
    return _then(
      _$RequestCancelledExceptionImpl(
        freezed == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$RequestCancelledExceptionImpl implements RequestCancelledException {
  const _$RequestCancelledExceptionImpl([this.message]);

  @override
  final String? message;

  @override
  String toString() {
    return 'ApiException.requestCancelled(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RequestCancelledExceptionImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RequestCancelledExceptionImplCopyWith<_$RequestCancelledExceptionImpl>
  get copyWith =>
      __$$RequestCancelledExceptionImplCopyWithImpl<
        _$RequestCancelledExceptionImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? message) badRequest,
    required TResult Function(String? message) unauthorized,
    required TResult Function(String? message) forbidden,
    required TResult Function(String? message) notFound,
    required TResult Function(String? message) internalServerError,
    required TResult Function(String? message) timeout,
    required TResult Function(String? message) noInternetConnection,
    required TResult Function(String? message) requestCancelled,
    required TResult Function(String? message) badCertificate,
    required TResult Function(String? message) unknown,
  }) {
    return requestCancelled(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? message)? badRequest,
    TResult? Function(String? message)? unauthorized,
    TResult? Function(String? message)? forbidden,
    TResult? Function(String? message)? notFound,
    TResult? Function(String? message)? internalServerError,
    TResult? Function(String? message)? timeout,
    TResult? Function(String? message)? noInternetConnection,
    TResult? Function(String? message)? requestCancelled,
    TResult? Function(String? message)? badCertificate,
    TResult? Function(String? message)? unknown,
  }) {
    return requestCancelled?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? message)? badRequest,
    TResult Function(String? message)? unauthorized,
    TResult Function(String? message)? forbidden,
    TResult Function(String? message)? notFound,
    TResult Function(String? message)? internalServerError,
    TResult Function(String? message)? timeout,
    TResult Function(String? message)? noInternetConnection,
    TResult Function(String? message)? requestCancelled,
    TResult Function(String? message)? badCertificate,
    TResult Function(String? message)? unknown,
    required TResult orElse(),
  }) {
    if (requestCancelled != null) {
      return requestCancelled(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BadRequestException value) badRequest,
    required TResult Function(UnauthorizedException value) unauthorized,
    required TResult Function(ForbiddenException value) forbidden,
    required TResult Function(NotFoundException value) notFound,
    required TResult Function(InternalServerErrorException value)
    internalServerError,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(NoInternetConnectionException value)
    noInternetConnection,
    required TResult Function(RequestCancelledException value) requestCancelled,
    required TResult Function(BadCertificateException value) badCertificate,
    required TResult Function(UnknownException value) unknown,
  }) {
    return requestCancelled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BadRequestException value)? badRequest,
    TResult? Function(UnauthorizedException value)? unauthorized,
    TResult? Function(ForbiddenException value)? forbidden,
    TResult? Function(NotFoundException value)? notFound,
    TResult? Function(InternalServerErrorException value)? internalServerError,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(NoInternetConnectionException value)?
    noInternetConnection,
    TResult? Function(RequestCancelledException value)? requestCancelled,
    TResult? Function(BadCertificateException value)? badCertificate,
    TResult? Function(UnknownException value)? unknown,
  }) {
    return requestCancelled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BadRequestException value)? badRequest,
    TResult Function(UnauthorizedException value)? unauthorized,
    TResult Function(ForbiddenException value)? forbidden,
    TResult Function(NotFoundException value)? notFound,
    TResult Function(InternalServerErrorException value)? internalServerError,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(NoInternetConnectionException value)? noInternetConnection,
    TResult Function(RequestCancelledException value)? requestCancelled,
    TResult Function(BadCertificateException value)? badCertificate,
    TResult Function(UnknownException value)? unknown,
    required TResult orElse(),
  }) {
    if (requestCancelled != null) {
      return requestCancelled(this);
    }
    return orElse();
  }
}

abstract class RequestCancelledException implements ApiException {
  const factory RequestCancelledException([final String? message]) =
      _$RequestCancelledExceptionImpl;

  @override
  String? get message;

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RequestCancelledExceptionImplCopyWith<_$RequestCancelledExceptionImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BadCertificateExceptionImplCopyWith<$Res>
    implements $ApiExceptionCopyWith<$Res> {
  factory _$$BadCertificateExceptionImplCopyWith(
    _$BadCertificateExceptionImpl value,
    $Res Function(_$BadCertificateExceptionImpl) then,
  ) = __$$BadCertificateExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? message});
}

/// @nodoc
class __$$BadCertificateExceptionImplCopyWithImpl<$Res>
    extends _$ApiExceptionCopyWithImpl<$Res, _$BadCertificateExceptionImpl>
    implements _$$BadCertificateExceptionImplCopyWith<$Res> {
  __$$BadCertificateExceptionImplCopyWithImpl(
    _$BadCertificateExceptionImpl _value,
    $Res Function(_$BadCertificateExceptionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = freezed}) {
    return _then(
      _$BadCertificateExceptionImpl(
        freezed == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$BadCertificateExceptionImpl implements BadCertificateException {
  const _$BadCertificateExceptionImpl([this.message]);

  @override
  final String? message;

  @override
  String toString() {
    return 'ApiException.badCertificate(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BadCertificateExceptionImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BadCertificateExceptionImplCopyWith<_$BadCertificateExceptionImpl>
  get copyWith =>
      __$$BadCertificateExceptionImplCopyWithImpl<
        _$BadCertificateExceptionImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? message) badRequest,
    required TResult Function(String? message) unauthorized,
    required TResult Function(String? message) forbidden,
    required TResult Function(String? message) notFound,
    required TResult Function(String? message) internalServerError,
    required TResult Function(String? message) timeout,
    required TResult Function(String? message) noInternetConnection,
    required TResult Function(String? message) requestCancelled,
    required TResult Function(String? message) badCertificate,
    required TResult Function(String? message) unknown,
  }) {
    return badCertificate(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? message)? badRequest,
    TResult? Function(String? message)? unauthorized,
    TResult? Function(String? message)? forbidden,
    TResult? Function(String? message)? notFound,
    TResult? Function(String? message)? internalServerError,
    TResult? Function(String? message)? timeout,
    TResult? Function(String? message)? noInternetConnection,
    TResult? Function(String? message)? requestCancelled,
    TResult? Function(String? message)? badCertificate,
    TResult? Function(String? message)? unknown,
  }) {
    return badCertificate?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? message)? badRequest,
    TResult Function(String? message)? unauthorized,
    TResult Function(String? message)? forbidden,
    TResult Function(String? message)? notFound,
    TResult Function(String? message)? internalServerError,
    TResult Function(String? message)? timeout,
    TResult Function(String? message)? noInternetConnection,
    TResult Function(String? message)? requestCancelled,
    TResult Function(String? message)? badCertificate,
    TResult Function(String? message)? unknown,
    required TResult orElse(),
  }) {
    if (badCertificate != null) {
      return badCertificate(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BadRequestException value) badRequest,
    required TResult Function(UnauthorizedException value) unauthorized,
    required TResult Function(ForbiddenException value) forbidden,
    required TResult Function(NotFoundException value) notFound,
    required TResult Function(InternalServerErrorException value)
    internalServerError,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(NoInternetConnectionException value)
    noInternetConnection,
    required TResult Function(RequestCancelledException value) requestCancelled,
    required TResult Function(BadCertificateException value) badCertificate,
    required TResult Function(UnknownException value) unknown,
  }) {
    return badCertificate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BadRequestException value)? badRequest,
    TResult? Function(UnauthorizedException value)? unauthorized,
    TResult? Function(ForbiddenException value)? forbidden,
    TResult? Function(NotFoundException value)? notFound,
    TResult? Function(InternalServerErrorException value)? internalServerError,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(NoInternetConnectionException value)?
    noInternetConnection,
    TResult? Function(RequestCancelledException value)? requestCancelled,
    TResult? Function(BadCertificateException value)? badCertificate,
    TResult? Function(UnknownException value)? unknown,
  }) {
    return badCertificate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BadRequestException value)? badRequest,
    TResult Function(UnauthorizedException value)? unauthorized,
    TResult Function(ForbiddenException value)? forbidden,
    TResult Function(NotFoundException value)? notFound,
    TResult Function(InternalServerErrorException value)? internalServerError,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(NoInternetConnectionException value)? noInternetConnection,
    TResult Function(RequestCancelledException value)? requestCancelled,
    TResult Function(BadCertificateException value)? badCertificate,
    TResult Function(UnknownException value)? unknown,
    required TResult orElse(),
  }) {
    if (badCertificate != null) {
      return badCertificate(this);
    }
    return orElse();
  }
}

abstract class BadCertificateException implements ApiException {
  const factory BadCertificateException([final String? message]) =
      _$BadCertificateExceptionImpl;

  @override
  String? get message;

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BadCertificateExceptionImplCopyWith<_$BadCertificateExceptionImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UnknownExceptionImplCopyWith<$Res>
    implements $ApiExceptionCopyWith<$Res> {
  factory _$$UnknownExceptionImplCopyWith(
    _$UnknownExceptionImpl value,
    $Res Function(_$UnknownExceptionImpl) then,
  ) = __$$UnknownExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? message});
}

/// @nodoc
class __$$UnknownExceptionImplCopyWithImpl<$Res>
    extends _$ApiExceptionCopyWithImpl<$Res, _$UnknownExceptionImpl>
    implements _$$UnknownExceptionImplCopyWith<$Res> {
  __$$UnknownExceptionImplCopyWithImpl(
    _$UnknownExceptionImpl _value,
    $Res Function(_$UnknownExceptionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = freezed}) {
    return _then(
      _$UnknownExceptionImpl(
        freezed == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$UnknownExceptionImpl implements UnknownException {
  const _$UnknownExceptionImpl([this.message]);

  @override
  final String? message;

  @override
  String toString() {
    return 'ApiException.unknown(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnknownExceptionImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UnknownExceptionImplCopyWith<_$UnknownExceptionImpl> get copyWith =>
      __$$UnknownExceptionImplCopyWithImpl<_$UnknownExceptionImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? message) badRequest,
    required TResult Function(String? message) unauthorized,
    required TResult Function(String? message) forbidden,
    required TResult Function(String? message) notFound,
    required TResult Function(String? message) internalServerError,
    required TResult Function(String? message) timeout,
    required TResult Function(String? message) noInternetConnection,
    required TResult Function(String? message) requestCancelled,
    required TResult Function(String? message) badCertificate,
    required TResult Function(String? message) unknown,
  }) {
    return unknown(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? message)? badRequest,
    TResult? Function(String? message)? unauthorized,
    TResult? Function(String? message)? forbidden,
    TResult? Function(String? message)? notFound,
    TResult? Function(String? message)? internalServerError,
    TResult? Function(String? message)? timeout,
    TResult? Function(String? message)? noInternetConnection,
    TResult? Function(String? message)? requestCancelled,
    TResult? Function(String? message)? badCertificate,
    TResult? Function(String? message)? unknown,
  }) {
    return unknown?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? message)? badRequest,
    TResult Function(String? message)? unauthorized,
    TResult Function(String? message)? forbidden,
    TResult Function(String? message)? notFound,
    TResult Function(String? message)? internalServerError,
    TResult Function(String? message)? timeout,
    TResult Function(String? message)? noInternetConnection,
    TResult Function(String? message)? requestCancelled,
    TResult Function(String? message)? badCertificate,
    TResult Function(String? message)? unknown,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BadRequestException value) badRequest,
    required TResult Function(UnauthorizedException value) unauthorized,
    required TResult Function(ForbiddenException value) forbidden,
    required TResult Function(NotFoundException value) notFound,
    required TResult Function(InternalServerErrorException value)
    internalServerError,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(NoInternetConnectionException value)
    noInternetConnection,
    required TResult Function(RequestCancelledException value) requestCancelled,
    required TResult Function(BadCertificateException value) badCertificate,
    required TResult Function(UnknownException value) unknown,
  }) {
    return unknown(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BadRequestException value)? badRequest,
    TResult? Function(UnauthorizedException value)? unauthorized,
    TResult? Function(ForbiddenException value)? forbidden,
    TResult? Function(NotFoundException value)? notFound,
    TResult? Function(InternalServerErrorException value)? internalServerError,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(NoInternetConnectionException value)?
    noInternetConnection,
    TResult? Function(RequestCancelledException value)? requestCancelled,
    TResult? Function(BadCertificateException value)? badCertificate,
    TResult? Function(UnknownException value)? unknown,
  }) {
    return unknown?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BadRequestException value)? badRequest,
    TResult Function(UnauthorizedException value)? unauthorized,
    TResult Function(ForbiddenException value)? forbidden,
    TResult Function(NotFoundException value)? notFound,
    TResult Function(InternalServerErrorException value)? internalServerError,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(NoInternetConnectionException value)? noInternetConnection,
    TResult Function(RequestCancelledException value)? requestCancelled,
    TResult Function(BadCertificateException value)? badCertificate,
    TResult Function(UnknownException value)? unknown,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown(this);
    }
    return orElse();
  }
}

abstract class UnknownException implements ApiException {
  const factory UnknownException([final String? message]) =
      _$UnknownExceptionImpl;

  @override
  String? get message;

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UnknownExceptionImplCopyWith<_$UnknownExceptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
