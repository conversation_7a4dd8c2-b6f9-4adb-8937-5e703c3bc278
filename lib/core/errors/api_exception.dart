import 'package:freezed_annotation/freezed_annotation.dart';

part 'api_exception.freezed.dart';

@freezed
class ApiException with _$ApiException implements Exception {
  const factory ApiException.badRequest([String? message]) = BadRequestException;
  const factory ApiException.unauthorized([String? message]) = UnauthorizedException;
  const factory ApiException.forbidden([String? message]) = ForbiddenException;
  const factory ApiException.notFound([String? message]) = NotFoundException;
  const factory ApiException.internalServerError([String? message]) = InternalServerErrorException;
  const factory ApiException.timeout([String? message]) = TimeoutException;
  const factory ApiException.noInternetConnection([String? message]) = NoInternetConnectionException;
  const factory ApiException.requestCancelled([String? message]) = RequestCancelledException;
  const factory ApiException.badCertificate([String? message]) = BadCertificateException;
  const factory ApiException.unknown([String? message]) = UnknownException;
}

extension ApiExceptionExtension on ApiException {
  String get message {
    return when(
      badRequest: (message) => message ?? 'Bad request',
      unauthorized: (message) => message ?? 'Unauthorized access',
      forbidden: (message) => message ?? 'Access forbidden',
      notFound: (message) => message ?? 'Resource not found',
      internalServerError: (message) => message ?? 'Internal server error',
      timeout: (message) => message ?? 'Request timeout',
      noInternetConnection: (message) => message ?? 'No internet connection',
      requestCancelled: (message) => message ?? 'Request cancelled',
      badCertificate: (message) => message ?? 'Bad certificate',
      unknown: (message) => message ?? 'Unknown error occurred',
    );
  }

  int get statusCode {
    return when(
      badRequest: (_) => 400,
      unauthorized: (_) => 401,
      forbidden: (_) => 403,
      notFound: (_) => 404,
      internalServerError: (_) => 500,
      timeout: (_) => 408,
      noInternetConnection: (_) => 0,
      requestCancelled: (_) => 0,
      badCertificate: (_) => 0,
      unknown: (_) => 0,
    );
  }
}
