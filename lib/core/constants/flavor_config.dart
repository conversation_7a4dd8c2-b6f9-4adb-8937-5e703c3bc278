enum Flavor {
  development,
  staging,
  production,
}

class FlavorConfig {
  FlavorConfig._({
    required this.flavor,
    required this.name,
    required this.baseUrl,
    required this.enableLogging,
  });

  factory FlavorConfig.development() {
    return FlavorConfig._(
      flavor: Flavor.development,
      name: '<PERSON><PERSON> (Dev)',
      baseUrl: 'https://dev-api.safea.com',
      enableLogging: true,
    );
  }

  factory FlavorConfig.staging() {
    return FlavorConfig._(
      flavor: Flavor.staging,
      name: '<PERSON><PERSON> (Staging)',
      baseUrl: 'https://staging-api.safea.com',
      enableLogging: true,
    );
  }

  factory FlavorConfig.production() {
    return FlavorConfig._(
      flavor: Flavor.production,
      name: 'Safea',
      baseUrl: 'https://api.safea.com',
      enableLogging: false,
    );
  }

  final Flavor flavor;
  final String name;
  final String baseUrl;
  final bool enableLogging;

  static FlavorConfig? _instance;

  static FlavorConfig get instance {
    assert(_instance != null, 'FlavorConfig must be initialized');
    return _instance!;
  }

  static void initialize(FlavorConfig config) {
    _instance = config;
  }

  bool get isDevelopment => flavor == Flavor.development;
  bool get isStaging => flavor == Flavor.staging;
  bool get isProduction => flavor == Flavor.production;
}
