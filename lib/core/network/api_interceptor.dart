import 'package:dio/dio.dart';
import 'package:safea/core/errors/api_exception.dart';

class ApiInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // Add authentication token if available
    // final token = getAuthToken();
    // if (token != null) {
    //   options.headers['Authorization'] = 'Bearer $token';
    // }

    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // Handle successful responses
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Handle errors and convert to custom exceptions
    final apiException = _handleError(err);
    handler.reject(
      DioException(
        requestOptions: err.requestOptions,
        error: apiException,
        type: err.type,
        response: err.response,
      ),
    );
  }

  ApiException _handleError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return ApiException.timeout();

      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = _extractErrorMessage(error.response?.data);

        switch (statusCode) {
          case 400:
            return ApiException.badRequest(message);
          case 401:
            return ApiException.unauthorized(message);
          case 403:
            return ApiException.forbidden(message);
          case 404:
            return ApiException.notFound(message);
          case 500:
            return ApiException.internalServerError(message);
          default:
            return ApiException.unknown(message);
        }

      case DioExceptionType.cancel:
        return ApiException.requestCancelled();

      case DioExceptionType.connectionError:
        return ApiException.noInternetConnection();

      case DioExceptionType.badCertificate:
        return ApiException.badCertificate();

      case DioExceptionType.unknown:
      default:
        return ApiException.unknown(error.message);
    }
  }

  String _extractErrorMessage(dynamic data) {
    if (data is Map<String, dynamic>) {
      return data['message'] as String? ??
          data['error'] as String? ??
          'An error occurred';
    }
    return 'An error occurred';
  }
}
