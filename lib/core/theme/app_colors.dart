import 'package:flutter/material.dart';

/// Application color constants
class AppColors {
  AppColors._();

  // Primary Colors
  static const Color primaryColor = Color(0xFF6366F1); // Indigo
  static const Color primaryVariant = Color(0xFF4F46E5);
  static const Color onPrimary = Color(0xFFFFFFFF);

  // Secondary Colors
  static const Color secondaryColor = Color(0xFF10B981); // Emerald
  static const Color secondaryVariant = Color(0xFF059669);
  static const Color onSecondary = Color(0xFFFFFFFF);

  // Surface Colors
  static const Color surface = Color(0xFFFFFFFF);
  static const Color onSurface = Color(0xFF1F2937);
  static const Color surfaceVariant = Color(0xFFF9FAFB);
  static const Color onSurfaceVariant = Color(0xFF6B7280);

  // Background Colors
  static const Color background = Color(0xFFFFFFFF);
  static const Color onBackground = Color(0xFF1F2937);

  // Error Colors
  static const Color error = Color(0xFFEF4444);
  static const Color onError = Color(0xFFFFFFFF);
  static const Color errorContainer = Color(0xFFFEE2E2);
  static const Color onErrorContainer = Color(0xFF991B1B);

  // Warning Colors
  static const Color warning = Color(0xFFF59E0B);
  static const Color onWarning = Color(0xFFFFFFFF);
  static const Color warningContainer = Color(0xFFFEF3C7);
  static const Color onWarningContainer = Color(0xFF92400E);

  // Success Colors
  static const Color success = Color(0xFF10B981);
  static const Color onSuccess = Color(0xFFFFFFFF);
  static const Color successContainer = Color(0xFFD1FAE5);
  static const Color onSuccessContainer = Color(0xFF065F46);

  // Info Colors
  static const Color info = Color(0xFF3B82F6);
  static const Color onInfo = Color(0xFFFFFFFF);
  static const Color infoContainer = Color(0xFFDBEAFE);
  static const Color onInfoContainer = Color(0xFF1E40AF);

  // Neutral Colors
  static const Color neutral50 = Color(0xFFFAFAFA);
  static const Color neutral100 = Color(0xFFF5F5F5);
  static const Color neutral200 = Color(0xFFE5E5E5);
  static const Color neutral300 = Color(0xFFD4D4D4);
  static const Color neutral400 = Color(0xFFA3A3A3);
  static const Color neutral500 = Color(0xFF737373);
  static const Color neutral600 = Color(0xFF525252);
  static const Color neutral700 = Color(0xFF404040);
  static const Color neutral800 = Color(0xFF262626);
  static const Color neutral900 = Color(0xFF171717);

  // Dark Theme Colors
  static const Color darkSurface = Color(0xFF1F2937);
  static const Color darkOnSurface = Color(0xFFFFFFFF);
  static const Color darkBackground = Color(0xFF111827);
  static const Color darkOnBackground = Color(0xFFFFFFFF);
  static const Color darkSurfaceVariant = Color(0xFF374151);
  static const Color darkOnSurfaceVariant = Color(0xFFD1D5DB);

  // Outline Colors
  static const Color outline = Color(0xFFD1D5DB);
  static const Color outlineVariant = Color(0xFFE5E7EB);
  static const Color darkOutline = Color(0xFF6B7280);
  static const Color darkOutlineVariant = Color(0xFF4B5563);

  // Shadow Colors
  static const Color shadow = Color(0xFF000000);
  static const Color scrim = Color(0xFF000000);

  /// Get light color scheme
  static ColorScheme get lightColorScheme => const ColorScheme.light(
        primary: primaryColor,
        onPrimary: onPrimary,
        primaryContainer: Color(0xFFE0E7FF),
        onPrimaryContainer: Color(0xFF312E81),
        secondary: secondaryColor,
        onSecondary: onSecondary,
        secondaryContainer: Color(0xFFD1FAE5),
        onSecondaryContainer: Color(0xFF065F46),
        tertiary: Color(0xFF8B5CF6),
        onTertiary: Color(0xFFFFFFFF),
        tertiaryContainer: Color(0xFFEDE9FE),
        onTertiaryContainer: Color(0xFF5B21B6),
        error: error,
        onError: onError,
        errorContainer: errorContainer,
        onErrorContainer: onErrorContainer,
        background: background,
        onBackground: onBackground,
        surface: surface,
        onSurface: onSurface,
        surfaceVariant: surfaceVariant,
        onSurfaceVariant: onSurfaceVariant,
        outline: outline,
        outlineVariant: outlineVariant,
        shadow: shadow,
        scrim: scrim,
        inverseSurface: neutral800,
        onInverseSurface: neutral100,
        inversePrimary: Color(0xFFA5B4FC),
      );

  /// Get dark color scheme
  static ColorScheme get darkColorScheme => const ColorScheme.dark(
        primary: Color(0xFFA5B4FC),
        onPrimary: Color(0xFF312E81),
        primaryContainer: Color(0xFF4338CA),
        onPrimaryContainer: Color(0xFFE0E7FF),
        secondary: Color(0xFF6EE7B7),
        onSecondary: Color(0xFF065F46),
        secondaryContainer: Color(0xFF047857),
        onSecondaryContainer: Color(0xFFD1FAE5),
        tertiary: Color(0xFFC4B5FD),
        onTertiary: Color(0xFF5B21B6),
        tertiaryContainer: Color(0xFF7C3AED),
        onTertiaryContainer: Color(0xFFEDE9FE),
        error: Color(0xFFF87171),
        onError: Color(0xFF991B1B),
        errorContainer: Color(0xFFDC2626),
        onErrorContainer: Color(0xFFFEE2E2),
        background: darkBackground,
        onBackground: darkOnBackground,
        surface: darkSurface,
        onSurface: darkOnSurface,
        surfaceVariant: darkSurfaceVariant,
        onSurfaceVariant: darkOnSurfaceVariant,
        outline: darkOutline,
        outlineVariant: darkOutlineVariant,
        shadow: shadow,
        scrim: scrim,
        inverseSurface: neutral100,
        onInverseSurface: neutral800,
        inversePrimary: primaryColor,
      );
}
