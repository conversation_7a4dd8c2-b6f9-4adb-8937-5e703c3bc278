import 'package:flutter/material.dart';

/// Responsive design system for the Safea app
/// Provides consistent sizing, spacing, and breakpoints across all devices
class AppDimensions {
  // Private constructor to prevent instantiation
  AppDimensions._();

  // ============================================================================
  // BREAKPOINTS
  // ============================================================================
  
  /// Mobile breakpoint (phones)
  static const double mobileBreakpoint = 600;
  
  /// Tablet breakpoint (tablets)
  static const double tabletBreakpoint = 1024;
  
  /// Desktop breakpoint (desktop/web)
  static const double desktopBreakpoint = 1440;

  // ============================================================================
  // BASE SPACING SYSTEM
  // ============================================================================
  
  /// Base spacing unit (4px) - all spacing should be multiples of this
  static const double baseSpacing = 4.0;
  
  /// Extra small spacing (4px)
  static const double spacingXs = baseSpacing;
  
  /// Small spacing (8px)
  static const double spacingSm = baseSpacing * 2;
  
  /// Medium spacing (16px)
  static const double spacingMd = baseSpacing * 4;
  
  /// Large spacing (24px)
  static const double spacingLg = baseSpacing * 6;
  
  /// Extra large spacing (32px)
  static const double spacingXl = baseSpacing * 8;
  
  /// Extra extra large spacing (40px)
  static const double spacingXxl = baseSpacing * 10;
  
  /// Extra extra extra large spacing (48px)
  static const double spacingXxxl = baseSpacing * 12;

  // ============================================================================
  // PADDING SYSTEM
  // ============================================================================
  
  /// Screen edge padding (24px) - standard padding from screen edges
  static const double screenPadding = 24.0;
  
  /// Card padding (16px) - padding inside cards and containers
  static const double cardPadding = 16.0;
  
  /// Button padding horizontal (24px)
  static const double buttonPaddingHorizontal = 24.0;
  
  /// Button padding vertical (12px)
  static const double buttonPaddingVertical = 12.0;
  
  /// Input field padding (16px)
  static const double inputPadding = 16.0;

  // ============================================================================
  // COMPONENT SIZES
  // ============================================================================
  
  /// Button heights
  static const double buttonHeightSmall = 36.0;
  static const double buttonHeightMedium = 48.0;
  static const double buttonHeightLarge = 56.0;
  
  /// Input field height
  static const double inputHeight = 56.0;
  
  /// App bar height
  static const double appBarHeight = 56.0;
  
  /// Bottom navigation height
  static const double bottomNavHeight = 80.0;
  
  /// Icon sizes
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 48.0;
  
  /// Avatar sizes
  static const double avatarSizeSmall = 32.0;
  static const double avatarSizeMedium = 48.0;
  static const double avatarSizeLarge = 64.0;
  static const double avatarSizeXLarge = 96.0;

  // ============================================================================
  // BORDER RADIUS SYSTEM
  // ============================================================================
  
  /// Small border radius (8px)
  static const double radiusSmall = 8.0;
  
  /// Medium border radius (12px)
  static const double radiusMedium = 12.0;
  
  /// Large border radius (16px)
  static const double radiusLarge = 16.0;
  
  /// Extra large border radius (24px)
  static const double radiusXLarge = 24.0;
  
  /// Button border radius (25px) - specific for buttons
  static const double radiusButton = 25.0;
  
  /// Card border radius (16px)
  static const double radiusCard = 16.0;

  // ============================================================================
  // RESPONSIVE SIZING FUNCTIONS
  // ============================================================================
  
  /// Get responsive width based on screen size
  /// [factor] - percentage of screen width (0.0 to 1.0)
  static double responsiveWidth(BuildContext context, double factor) {
    return MediaQuery.of(context).size.width * factor;
  }
  
  /// Get responsive height based on screen size
  /// [factor] - percentage of screen height (0.0 to 1.0)
  static double responsiveHeight(BuildContext context, double factor) {
    return MediaQuery.of(context).size.height * factor;
  }
  
  /// Get responsive font size based on screen width
  /// [baseFontSize] - base font size for mobile
  /// [scaleFactor] - how much to scale on larger screens (default: 1.2)
  static double responsiveFontSize(
    BuildContext context, 
    double baseFontSize, {
    double scaleFactor = 1.2,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= tabletBreakpoint) {
      return baseFontSize * scaleFactor;
    }
    return baseFontSize;
  }
  
  /// Get responsive spacing based on screen size
  /// [baseSpacing] - base spacing for mobile
  /// [scaleFactor] - how much to scale on larger screens (default: 1.5)
  static double responsiveSpacing(
    BuildContext context, 
    double baseSpacing, {
    double scaleFactor = 1.5,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= tabletBreakpoint) {
      return baseSpacing * scaleFactor;
    }
    return baseSpacing;
  }

  // ============================================================================
  // DEVICE TYPE DETECTION
  // ============================================================================
  
  /// Check if current device is mobile
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }
  
  /// Check if current device is tablet
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }
  
  /// Check if current device is desktop
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }

  // ============================================================================
  // SAFE AREA HELPERS
  // ============================================================================
  
  /// Get safe area top padding
  static double safeAreaTop(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }
  
  /// Get safe area bottom padding
  static double safeAreaBottom(BuildContext context) {
    return MediaQuery.of(context).padding.bottom;
  }
  
  /// Get available screen height (excluding safe areas)
  static double availableHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.height - 
           mediaQuery.padding.top - 
           mediaQuery.padding.bottom;
  }
  
  /// Get available screen width
  static double availableWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }
}
