import 'package:flutter/material.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/responsive_extensions.dart';

/// App text styles based on the Safea design system
class AppTextStyles {
  // Base font family
  static const String fontFamily = 'SF Pro Display'; // iOS system font

  // Display styles (largest text)
  static const TextStyle displayLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 57,
    fontWeight: FontWeight.w400,
    height: 1.12,
    letterSpacing: -0.25,
    color: AppColors.onBackground,
  );

  static const TextStyle displayMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 45,
    fontWeight: FontWeight.w400,
    height: 1.16,
    color: AppColors.onBackground,
  );

  static const TextStyle displaySmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 36,
    fontWeight: FontWeight.w400,
    height: 1.22,
    color: AppColors.onBackground,
  );

  // Headline styles
  static const TextStyle headlineLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 32,
    fontWeight: FontWeight.w400,
    height: 1.25,
    color: AppColors.onBackground,
  );

  static const TextStyle headlineMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 28,
    fontWeight: FontWeight.w400,
    height: 1.29,
    color: AppColors.onBackground,
  );

  static const TextStyle headlineSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 24,
    fontWeight: FontWeight.w400,
    height: 1.33,
    color: AppColors.onBackground,
  );

  // Title styles
  static const TextStyle titleLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 22,
    fontWeight: FontWeight.w400,
    height: 1.27,
    color: AppColors.onBackground,
  );

  static const TextStyle titleMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 1.50,
    letterSpacing: 0.15,
    color: AppColors.onBackground,
  );

  static const TextStyle titleSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.43,
    letterSpacing: 0.10,
    color: AppColors.onBackground,
  );

  // Body styles
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    height: 1.50,
    letterSpacing: 0.50,
    color: AppColors.onBackground,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 1.43,
    letterSpacing: 0.25,
    color: AppColors.onBackground,
  );

  static const TextStyle bodySmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    height: 1.33,
    letterSpacing: 0.40,
    color: AppColors.onBackground,
  );

  // Label styles
  static const TextStyle labelLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.43,
    letterSpacing: 0.10,
    color: AppColors.onBackground,
  );

  static const TextStyle labelMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w500,
    height: 1.33,
    letterSpacing: 0.50,
    color: AppColors.onBackground,
  );

  static const TextStyle labelSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 11,
    fontWeight: FontWeight.w500,
    height: 1.45,
    letterSpacing: 0.50,
    color: AppColors.onBackground,
  );

  // Custom app-specific styles

  // App logo text
  static const TextStyle logo = TextStyle(
    fontFamily: fontFamily,
    fontSize: 24,
    fontWeight: FontWeight.w300,
    color: AppColors.onBackground,
    letterSpacing: 2.0,
  );

  // Main tagline on start screen
  static const TextStyle tagline = TextStyle(
    fontFamily: fontFamily,
    fontSize: 32,
    fontWeight: FontWeight.w600,
    height: 1.2,
    color: AppColors.onBackground,
  );

  // Subtitle text
  static const TextStyle subtitle = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    height: 1.4,
    color: AppColors.onBackgroundSecondary,
  );

  // Button text styles
  static const TextStyle buttonPrimary = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: AppColors.buttonPrimaryText,
  );

  static const TextStyle buttonSecondary = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: AppColors.buttonSecondaryText,
  );

  static const TextStyle buttonOutline = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: AppColors.buttonOutlineText,
  );

  // Input field styles
  static const TextStyle inputLabel = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: AppColors.onBackgroundSecondary,
  );

  static const TextStyle inputText = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: AppColors.inputText,
  );

  static const TextStyle inputHint = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: AppColors.inputHint,
  );

  // Error text
  static const TextStyle error = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: AppColors.error,
  );

  // Success text
  static const TextStyle success = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: AppColors.success,
  );

  // Timer text
  static const TextStyle timerLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 48,
    fontWeight: FontWeight.w300,
    color: AppColors.timerActive,
    fontFeatures: [FontFeature.tabularFigures()],
  );

  static const TextStyle timerMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 32,
    fontWeight: FontWeight.w400,
    color: AppColors.timerActive,
    fontFeatures: [FontFeature.tabularFigures()],
  );

  // Navigation text
  static const TextStyle navigationActive = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w600,
    color: AppColors.navigationSelected,
  );

  static const TextStyle navigationInactive = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: AppColors.navigationUnselected,
  );

  // Code input text
  static const TextStyle codeInput = TextStyle(
    fontFamily: fontFamily,
    fontSize: 24,
    fontWeight: FontWeight.w600,
    color: AppColors.onBackground,
    fontFeatures: [FontFeature.tabularFigures()],
  );

  // ============================================================================
  // RESPONSIVE TEXT STYLES
  // ============================================================================

  /// Get responsive tagline style based on screen size
  static TextStyle responsiveTagline(BuildContext context) {
    final baseFontSize = 32.0;
    final responsiveFontSize = context.responsiveFont(baseFontSize);
    return tagline.copyWith(fontSize: responsiveFontSize);
  }

  /// Get responsive display large style based on screen size
  static TextStyle responsiveDisplayLarge(BuildContext context) {
    final baseFontSize = 57.0;
    final responsiveFontSize = context.responsiveFont(baseFontSize);
    return displayLarge.copyWith(fontSize: responsiveFontSize);
  }

  /// Get responsive headline large style based on screen size
  static TextStyle responsiveHeadlineLarge(BuildContext context) {
    final baseFontSize = 32.0;
    final responsiveFontSize = context.responsiveFont(baseFontSize);
    return headlineLarge.copyWith(fontSize: responsiveFontSize);
  }

  /// Get responsive headline medium style based on screen size
  static TextStyle responsiveHeadlineMedium(BuildContext context) {
    final baseFontSize = 28.0;
    final responsiveFontSize = context.responsiveFont(baseFontSize);
    return headlineMedium.copyWith(fontSize: responsiveFontSize);
  }

  /// Get responsive timer large style based on screen size
  static TextStyle responsiveTimerLarge(BuildContext context) {
    final baseFontSize = 48.0;
    final responsiveFontSize = context.responsiveFont(
      baseFontSize,
      scaleFactor: 1.3,
    );
    return timerLarge.copyWith(fontSize: responsiveFontSize);
  }

  /// Get responsive button text style based on screen size
  static TextStyle responsiveButtonPrimary(BuildContext context) {
    final baseFontSize = 16.0;
    final responsiveFontSize = context.responsiveFont(
      baseFontSize,
      scaleFactor: 1.1,
    );
    return buttonPrimary.copyWith(fontSize: responsiveFontSize);
  }
}
