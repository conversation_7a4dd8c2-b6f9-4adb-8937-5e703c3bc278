import 'package:flutter/material.dart';
import 'package:safea/core/theme/app_dimensions.dart';

/// Extension methods on BuildContext for easy access to responsive utilities
extension ResponsiveContext on BuildContext {
  // ============================================================================
  // SCREEN DIMENSIONS
  // ============================================================================
  
  /// Get screen width
  double get screenWidth => MediaQuery.of(this).size.width;
  
  /// Get screen height
  double get screenHeight => MediaQuery.of(this).size.height;
  
  /// Get available height (excluding safe areas)
  double get availableHeight => AppDimensions.availableHeight(this);
  
  /// Get available width
  double get availableWidth => AppDimensions.availableWidth(this);

  // ============================================================================
  // RESPONSIVE SIZING
  // ============================================================================
  
  /// Get responsive width as percentage of screen width
  /// Example: context.widthPercent(0.8) = 80% of screen width
  double widthPercent(double factor) => AppDimensions.responsiveWidth(this, factor);
  
  /// Get responsive height as percentage of screen height
  /// Example: context.heightPercent(0.5) = 50% of screen height
  double heightPercent(double factor) => AppDimensions.responsiveHeight(this, factor);
  
  /// Get responsive font size
  /// Example: context.responsiveFont(16) = 16px on mobile, scaled on tablet+
  double responsiveFont(double baseFontSize, {double scaleFactor = 1.2}) =>
      AppDimensions.responsiveFontSize(this, baseFontSize, scaleFactor: scaleFactor);
  
  /// Get responsive spacing
  /// Example: context.responsiveSpacing(16) = 16px on mobile, scaled on tablet+
  double responsiveSpacing(double baseSpacing, {double scaleFactor = 1.5}) =>
      AppDimensions.responsiveSpacing(this, baseSpacing, scaleFactor: scaleFactor);

  // ============================================================================
  // DEVICE TYPE DETECTION
  // ============================================================================
  
  /// Check if current device is mobile
  bool get isMobile => AppDimensions.isMobile(this);
  
  /// Check if current device is tablet
  bool get isTablet => AppDimensions.isTablet(this);
  
  /// Check if current device is desktop
  bool get isDesktop => AppDimensions.isDesktop(this);

  // ============================================================================
  // SAFE AREA
  // ============================================================================
  
  /// Get safe area top padding
  double get safeAreaTop => AppDimensions.safeAreaTop(this);
  
  /// Get safe area bottom padding
  double get safeAreaBottom => AppDimensions.safeAreaBottom(this);
  
  /// Get MediaQuery padding
  EdgeInsets get padding => MediaQuery.of(this).padding;

  // ============================================================================
  // COMMON RESPONSIVE PATTERNS
  // ============================================================================
  
  /// Get responsive padding for screen edges
  /// Returns larger padding on tablets/desktop
  EdgeInsets get screenPadding {
    final basePadding = AppDimensions.screenPadding;
    final responsivePadding = responsiveSpacing(basePadding);
    return EdgeInsets.all(responsivePadding);
  }
  
  /// Get responsive horizontal padding for screen edges
  EdgeInsets get screenPaddingHorizontal {
    final basePadding = AppDimensions.screenPadding;
    final responsivePadding = responsiveSpacing(basePadding);
    return EdgeInsets.symmetric(horizontal: responsivePadding);
  }
  
  /// Get responsive vertical padding for screen edges
  EdgeInsets get screenPaddingVertical {
    final basePadding = AppDimensions.screenPadding;
    final responsivePadding = responsiveSpacing(basePadding);
    return EdgeInsets.symmetric(vertical: responsivePadding);
  }
  
  /// Get responsive card padding
  EdgeInsets get cardPadding {
    final basePadding = AppDimensions.cardPadding;
    final responsivePadding = responsiveSpacing(basePadding, scaleFactor: 1.25);
    return EdgeInsets.all(responsivePadding);
  }
  
  /// Get responsive button height
  double get buttonHeight {
    return isMobile 
        ? AppDimensions.buttonHeightMedium 
        : AppDimensions.buttonHeightLarge;
  }
  
  /// Get responsive input height
  double get inputHeight {
    return responsiveSpacing(AppDimensions.inputHeight, scaleFactor: 1.1);
  }

  // ============================================================================
  // LAYOUT HELPERS
  // ============================================================================
  
  /// Get responsive column width for forms
  /// Returns full width on mobile, constrained width on larger screens
  double get formWidth {
    if (isMobile) return screenWidth;
    if (isTablet) return screenWidth * 0.7;
    return 600; // Fixed max width for desktop
  }
  
  /// Get responsive content width
  /// Returns full width on mobile, constrained width on larger screens
  double get contentWidth {
    if (isMobile) return screenWidth;
    if (isTablet) return screenWidth * 0.8;
    return 800; // Fixed max width for desktop
  }
  
  /// Get responsive dialog width
  double get dialogWidth {
    if (isMobile) return screenWidth * 0.9;
    if (isTablet) return screenWidth * 0.6;
    return 500; // Fixed width for desktop
  }

  // ============================================================================
  // SPACING SHORTCUTS
  // ============================================================================
  
  /// Extra small spacing (4px)
  double get spacingXs => AppDimensions.spacingXs;
  
  /// Small spacing (8px)
  double get spacingSm => AppDimensions.spacingSm;
  
  /// Medium spacing (16px)
  double get spacingMd => AppDimensions.spacingMd;
  
  /// Large spacing (24px)
  double get spacingLg => AppDimensions.spacingLg;
  
  /// Extra large spacing (32px)
  double get spacingXl => AppDimensions.spacingXl;
  
  /// Extra extra large spacing (40px)
  double get spacingXxl => AppDimensions.spacingXxl;
  
  /// Extra extra extra large spacing (48px)
  double get spacingXxxl => AppDimensions.spacingXxxl;

  // ============================================================================
  // RADIUS SHORTCUTS
  // ============================================================================
  
  /// Small border radius (8px)
  double get radiusSmall => AppDimensions.radiusSmall;
  
  /// Medium border radius (12px)
  double get radiusMedium => AppDimensions.radiusMedium;
  
  /// Large border radius (16px)
  double get radiusLarge => AppDimensions.radiusLarge;
  
  /// Extra large border radius (24px)
  double get radiusXLarge => AppDimensions.radiusXLarge;
  
  /// Button border radius (25px)
  double get radiusButton => AppDimensions.radiusButton;
  
  /// Card border radius (16px)
  double get radiusCard => AppDimensions.radiusCard;
}
