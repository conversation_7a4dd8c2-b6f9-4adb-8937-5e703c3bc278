import 'package:flutter/material.dart';

/// App color palette based on the Safea design system
class AppColors {
  // Primary brand colors
  static const Color primary = Color(0xFF00FF88); // Bright green from designs
  static const Color primaryDark = Color(0xFF00CC6A);
  static const Color primaryLight = Color(0xFF33FF99);
  
  // Background colors
  static const Color background = Color(0xFF000000); // Black background
  static const Color backgroundSecondary = Color(0xFF111111);
  static const Color backgroundTertiary = Color(0xFF1A1A1A);
  
  // Surface colors
  static const Color surface = Color(0xFF1E1E1E);
  static const Color surfaceVariant = Color(0xFF2A2A2A);
  static const Color surfaceContainer = Color(0xFF333333);
  
  // Text colors
  static const Color onBackground = Color(0xFFFFFFFF); // White text
  static const Color onBackgroundSecondary = Color(0xFFB3B3B3); // Light gray
  static const Color onBackgroundTertiary = Color(0xFF808080); // Medium gray
  static const Color onSurface = Color(0xFFFFFFFF);
  static const Color onSurfaceVariant = Color(0xFFCCCCCC);
  
  // Accent colors
  static const Color accent = Color(0xFF00FF88);
  static const Color accentSecondary = Color(0xFF88FF00);
  
  // Status colors
  static const Color success = Color(0xFF00FF88);
  static const Color warning = Color(0xFFFFAA00);
  static const Color error = Color(0xFFFF4444);
  static const Color info = Color(0xFF0088FF);
  
  // Interactive colors
  static const Color interactive = Color(0xFF00FF88);
  static const Color interactiveHover = Color(0xFF33FF99);
  static const Color interactivePressed = Color(0xFF00CC6A);
  static const Color interactiveDisabled = Color(0xFF404040);
  
  // Border colors
  static const Color border = Color(0xFF333333);
  static const Color borderFocus = Color(0xFF00FF88);
  static const Color borderError = Color(0xFFFF4444);
  
  // Input field colors
  static const Color inputBackground = Color(0xFF1A1A1A);
  static const Color inputBorder = Color(0xFF333333);
  static const Color inputBorderFocus = Color(0xFF00FF88);
  static const Color inputText = Color(0xFFFFFFFF);
  static const Color inputHint = Color(0xFF808080);
  
  // Button colors
  static const Color buttonPrimary = Color(0xFFFFFFFF);
  static const Color buttonPrimaryText = Color(0xFF000000);
  static const Color buttonSecondary = Color(0xFF333333);
  static const Color buttonSecondaryText = Color(0xFFFFFFFF);
  static const Color buttonOutline = Color(0xFF00FF88);
  static const Color buttonOutlineText = Color(0xFF00FF88);
  
  // Gradient colors for the signature green glow effect
  static const List<Color> primaryGradient = [
    Color(0xFF00FF88),
    Color(0xFF00CC6A),
    Color(0xFF009944),
  ];
  
  static const List<Color> backgroundGradient = [
    Color(0xFF000000),
    Color(0xFF001100),
    Color(0xFF000000),
  ];
  
  // Glow effect colors
  static const Color glowPrimary = Color(0x4400FF88); // 26% opacity
  static const Color glowSecondary = Color(0x2200FF88); // 13% opacity
  static const Color glowTertiary = Color(0x1100FF88); // 6% opacity
  
  // Overlay colors
  static const Color overlay = Color(0x80000000); // 50% black
  static const Color overlayLight = Color(0x40000000); // 25% black
  static const Color overlayHeavy = Color(0xCC000000); // 80% black
  
  // Timer and progress colors
  static const Color timerActive = Color(0xFF00FF88);
  static const Color timerInactive = Color(0xFF333333);
  static const Color progressBackground = Color(0xFF1A1A1A);
  static const Color progressForeground = Color(0xFF00FF88);
  
  // Card and container colors
  static const Color cardBackground = Color(0xFF1A1A1A);
  static const Color cardBorder = Color(0xFF333333);
  static const Color cardShadow = Color(0x40000000);
  
  // Navigation colors
  static const Color navigationBackground = Color(0xFF111111);
  static const Color navigationSelected = Color(0xFF00FF88);
  static const Color navigationUnselected = Color(0xFF666666);
  
  // Verification code colors
  static const Color codeInputBackground = Color(0xFF1A1A1A);
  static const Color codeInputBorder = Color(0xFF333333);
  static const Color codeInputActive = Color(0xFF00FF88);
  static const Color codeInputFilled = Color(0xFF00FF88);
}
