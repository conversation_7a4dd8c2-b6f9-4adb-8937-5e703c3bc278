// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'storage_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$needsSetupHash() => r'01658f57172f9a7180519d4dc553113ebb48cc57';

/// Provider that combines all setup states to determine if setup is needed
///
/// Copied from [needsSetup].
@ProviderFor(needsSetup)
final needsSetupProvider = AutoDisposeProvider<bool>.internal(
  needsSetup,
  name: r'needsSetupProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$needsSetupHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NeedsSetupRef = AutoDisposeProviderRef<bool>;
String _$setupStatusHash() => r'ce0b4e4815ddfd06436e4870c794d6916429e594';

/// Provider for setup completion status
///
/// Copied from [SetupStatus].
@ProviderFor(SetupStatus)
final setupStatusProvider =
    AutoDisposeNotifierProvider<SetupStatus, bool>.internal(
      SetupStatus.new,
      name: r'setupStatusProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$setupStatusHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SetupStatus = AutoDisposeNotifier<bool>;
String _$permissionsStatusHash() => r'45c7774211c61249f6bbf1d93b84a2e0e1ca6d25';

/// Provider for permissions status
///
/// Copied from [PermissionsStatus].
@ProviderFor(PermissionsStatus)
final permissionsStatusProvider =
    AutoDisposeNotifierProvider<PermissionsStatus, bool>.internal(
      PermissionsStatus.new,
      name: r'permissionsStatusProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$permissionsStatusHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PermissionsStatus = AutoDisposeNotifier<bool>;
String _$agreementsStatusHash() => r'9281a2efda950b1020a50da40cc422cb4235be62';

/// Provider for agreements acceptance status
///
/// Copied from [AgreementsStatus].
@ProviderFor(AgreementsStatus)
final agreementsStatusProvider =
    AutoDisposeNotifierProvider<AgreementsStatus, bool>.internal(
      AgreementsStatus.new,
      name: r'agreementsStatusProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$agreementsStatusHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AgreementsStatus = AutoDisposeNotifier<bool>;
String _$timerSetupStatusHash() => r'c9614e3a4f1a6dfdfd52ea789c049ac98ee726da';

/// Provider for timer setup completion status
///
/// Copied from [TimerSetupStatus].
@ProviderFor(TimerSetupStatus)
final timerSetupStatusProvider =
    AutoDisposeNotifierProvider<TimerSetupStatus, bool>.internal(
      TimerSetupStatus.new,
      name: r'timerSetupStatusProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$timerSetupStatusHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TimerSetupStatus = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
