import 'package:shared_preferences/shared_preferences.dart';

/// Service for handling local storage operations
class StorageService {
  static const String _setupCompletedKey = 'setup_completed';
  static const String _permissionsGrantedKey = 'permissions_granted';
  static const String _agreementsAcceptedKey = 'agreements_accepted';
  static const String _timerSetupCompletedKey = 'timer_setup_completed';

  static SharedPreferences? _prefs;

  /// Initialize the storage service
  static Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Get SharedPreferences instance
  static SharedPreferences get _preferences {
    if (_prefs == null) {
      throw Exception(
        'StorageService not initialized. Call initialize() first.',
      );
    }
    return _prefs!;
  }

  // Setup completion status
  // COMMENTED OUT FOR TESTING - Always return false to see setup screens
  static bool get isSetupCompleted =>
      false; // _preferences.getBool(_setupCompletedKey) ?? false;
  static Future<void> setSetupCompleted(bool completed) async {
    // await _preferences.setBool(_setupCompletedKey, completed);
  }

  // Permissions status
  // COMMENTED OUT FOR TESTING - Always return false to see setup screens
  static bool get arePermissionsGranted =>
      false; // _preferences.getBool(_permissionsGrantedKey) ?? false;
  static Future<void> setPermissionsGranted(bool granted) async {
    // await _preferences.setBool(_permissionsGrantedKey, granted);
  }

  // Agreements acceptance status
  // COMMENTED OUT FOR TESTING - Always return false to see setup screens
  static bool get areAgreementsAccepted =>
      false; // _preferences.getBool(_agreementsAcceptedKey) ?? false;
  static Future<void> setAgreementsAccepted(bool accepted) async {
    // await _preferences.setBool(_agreementsAcceptedKey, accepted);
  }

  // Timer setup completion status
  // COMMENTED OUT FOR TESTING - Always return false to see setup screens
  static bool get isTimerSetupCompleted =>
      false; // _preferences.getBool(_timerSetupCompletedKey) ?? false;
  static Future<void> setTimerSetupCompleted(bool completed) async {
    // await _preferences.setBool(_timerSetupCompletedKey, completed);
  }

  /// Clear all setup-related data (useful for testing or reset)
  static Future<void> clearSetupData() async {
    await Future.wait([
      _preferences.remove(_setupCompletedKey),
      _preferences.remove(_permissionsGrantedKey),
      _preferences.remove(_agreementsAcceptedKey),
      _preferences.remove(_timerSetupCompletedKey),
    ]);
  }

  /// Check if the complete setup flow is finished
  static bool get isCompleteSetupFinished {
    return isSetupCompleted &&
        arePermissionsGranted &&
        areAgreementsAccepted &&
        isTimerSetupCompleted;
  }
}
