import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:safea/core/storage/storage_service.dart';

part 'storage_providers.g.dart';

/// Provider for setup completion status
@riverpod
class SetupStatus extends _$SetupStatus {
  @override
  bool build() {
    return StorageService.isSetupCompleted;
  }

  /// Mark setup as completed
  Future<void> setCompleted(bool completed) async {
    await StorageService.setSetupCompleted(completed);
    state = completed;
  }

  /// Check if complete setup flow is finished
  bool get isCompleteSetupFinished => StorageService.isCompleteSetupFinished;
}

/// Provider for permissions status
@riverpod
class PermissionsStatus extends _$PermissionsStatus {
  @override
  bool build() {
    return StorageService.arePermissionsGranted;
  }

  /// Update permissions status
  Future<void> setGranted(bool granted) async {
    await StorageService.setPermissionsGranted(granted);
    state = granted;
  }
}

/// Provider for agreements acceptance status
@riverpod
class AgreementsStatus extends _$AgreementsStatus {
  @override
  bool build() {
    return StorageService.areAgreementsAccepted;
  }

  /// Update agreements acceptance status
  Future<void> setAccepted(bool accepted) async {
    await StorageService.setAgreementsAccepted(accepted);
    state = accepted;
  }
}

/// Provider for timer setup completion status
@riverpod
class TimerSetupStatus extends _$TimerSetupStatus {
  @override
  bool build() {
    return StorageService.isTimerSetupCompleted;
  }

  /// Update timer setup completion status
  Future<void> setCompleted(bool completed) async {
    await StorageService.setTimerSetupCompleted(completed);
    state = completed;
  }
}

/// Provider that combines all setup states to determine if setup is needed
@riverpod
bool needsSetup(NeedsSetupRef ref) {
  final setupCompleted = ref.watch(setupStatusProvider);
  final permissionsGranted = ref.watch(permissionsStatusProvider);
  final agreementsAccepted = ref.watch(agreementsStatusProvider);
  final timerSetupCompleted = ref.watch(timerSetupStatusProvider);

  // Setup is needed if any of the required steps are not completed
  return !setupCompleted || !permissionsGranted || !agreementsAccepted || !timerSetupCompleted;
}
