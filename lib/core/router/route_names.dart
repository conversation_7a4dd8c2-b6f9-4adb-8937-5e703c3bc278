class RouteNames {
  // Legacy routes (to be removed)
  static const String userList = '/users';
  static const String userDetail = '/users/:id';

  // App navigation flow
  static const String splash = '/';
  static const String start = '/start';

  // Authentication flow
  static const String signUp = '/auth/signup';
  static const String signUpProfile = '/auth/signup/profile';
  static const String login = '/auth/login';
  static const String verification = '/auth/verification';

  // Setup flow
  static const String setupStart = '/setup/start';
  static const String setupTracking = '/setup/tracking';
  static const String setupAvailableTimes = '/setup/available-times';
  static const String setupDone = '/setup/done';

  // Legacy setup routes (to be removed)
  static const String setup01 = '/setup/01';
  static const String setup01Agb = '/setup/01/agb';
  static const String setupTimer = '/setup/timer';

  // Dashboard flow
  static const String dashboardHome = '/dashboard';
  static const String dashboardUserTracking = '/dashboard/user-tracking';
  static const String dashboardUserDone = '/dashboard/user-done';

  // Settings
  static const String settings = '/settings';
}
