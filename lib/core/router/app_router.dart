import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/storage/storage_providers.dart';
import 'package:safea/features/auth/presentation/pages/login_screen.dart';
import 'package:safea/features/auth/presentation/pages/sign_up_profile_screen.dart';
import 'package:safea/features/auth/presentation/pages/sign_up_screen.dart';
import 'package:safea/features/auth/presentation/pages/splash_screen.dart';
import 'package:safea/features/auth/presentation/pages/start_screen.dart';
import 'package:safea/features/auth/presentation/pages/verification_screen.dart';
import 'package:safea/features/setup/presentation/pages/setup01_screen.dart';
import 'package:safea/features/setup/presentation/pages/setup01_agb_screen.dart';
import 'package:safea/features/setup/presentation/pages/setup_timer_screen.dart';
import 'package:safea/features/setup/presentation/pages/setup_permissions_screen.dart';
import 'package:safea/features/setup/presentation/pages/setup_done_screen.dart';
import 'package:safea/features/setup/presentation/pages/setup_tracking_screen.dart';
import 'package:safea/features/setup/presentation/pages/setup_available_times_screen.dart';
import 'package:safea/features/setup/presentation/pages/setup_start_screen.dart';
import 'package:safea/features/setup/presentation/pages/setup_completion_screen.dart';
import 'package:safea/features/dashboard/presentation/pages/dashboard_home_screen.dart';
import 'package:safea/features/settings/presentation/pages/settings_screen.dart';
import 'package:safea/features/user/presentation/pages/user_list_page.dart';

part 'app_router.g.dart';

@riverpod
GoRouter appRouter(Ref ref) {
  return GoRouter(
    initialLocation: RouteNames.splash,
    debugLogDiagnostics: true,
    redirect: (context, state) {
      // Don't redirect if we're already on auth or setup screens
      final authRoutes = [
        RouteNames.splash,
        RouteNames.start,
        RouteNames.signUp,
        RouteNames.signUpProfile,
        RouteNames.login,
        RouteNames.verification,
      ];

      final setupRoutes = [
        RouteNames.setupStart,
        RouteNames.setupTracking,
        RouteNames.setupAvailableTimes,
        RouteNames.setupDone,
        // Legacy routes
        RouteNames.setup01,
        RouteNames.setup01Agb,
        RouteNames.setupTimer,
      ];

      if (authRoutes.contains(state.matchedLocation) ||
          setupRoutes.contains(state.matchedLocation)) {
        return null; // No redirect needed
      }

      // Check if setup is needed
      final needsSetup = ref.read(needsSetupProvider);
      if (needsSetup) {
        return RouteNames.setupTracking;
      }

      return null; // No redirect needed
    },
    routes: [
      // Splash and Start
      GoRoute(
        path: RouteNames.splash,
        name: RouteNames.splash,
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: RouteNames.start,
        name: RouteNames.start,
        builder: (context, state) => const StartScreen(),
      ),

      // Authentication routes
      GoRoute(
        path: RouteNames.signUp,
        name: RouteNames.signUp,
        builder: (context, state) => const SignUpScreen(),
      ),
      GoRoute(
        path: RouteNames.signUpProfile,
        name: RouteNames.signUpProfile,
        builder: (context, state) => const SignUpProfileScreen(),
      ),
      GoRoute(
        path: RouteNames.login,
        name: RouteNames.login,
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: RouteNames.verification,
        name: RouteNames.verification,
        builder: (context, state) => const VerificationScreen(),
      ),

      // Setup routes
      GoRoute(
        path: RouteNames.setupStart,
        name: RouteNames.setupStart,
        builder: (context, state) => const SetupStartScreen(),
      ),
      GoRoute(
        path: RouteNames.setupTracking,
        name: RouteNames.setupTracking,
        builder: (context, state) => const SetupTrackingScreen(),
      ),
      GoRoute(
        path: RouteNames.setupAvailableTimes,
        name: RouteNames.setupAvailableTimes,
        builder: (context, state) => const SetupAvailableTimesScreen(),
      ),
      GoRoute(
        path: RouteNames.setupDone,
        name: RouteNames.setupDone,
        builder: (context, state) => const SetupCompletionScreen(),
      ),

      // Legacy setup routes (to be removed)
      GoRoute(
        path: RouteNames.setup01,
        name: RouteNames.setup01,
        builder: (context, state) => const Setup01Screen(),
      ),
      GoRoute(
        path: RouteNames.setup01Agb,
        name: RouteNames.setup01Agb,
        builder: (context, state) => const Setup01AgbScreen(),
      ),
      GoRoute(
        path: RouteNames.setupTimer,
        name: RouteNames.setupTimer,
        builder: (context, state) => const SetupTimerScreen(),
      ),

      // Dashboard routes
      GoRoute(
        path: RouteNames.dashboardHome,
        name: RouteNames.dashboardHome,
        builder: (context, state) => const DashboardHomeScreen(),
      ),

      // Settings route
      GoRoute(
        path: RouteNames.settings,
        name: RouteNames.settings,
        builder: (context, state) => const SettingsScreen(),
      ),

      // Legacy route (to be removed)
      GoRoute(
        path: RouteNames.userList,
        name: RouteNames.userList,
        builder: (context, state) => const UserListPage(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              state.error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(RouteNames.splash),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
}
