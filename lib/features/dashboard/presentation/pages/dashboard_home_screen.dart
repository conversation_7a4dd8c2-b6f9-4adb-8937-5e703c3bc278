import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/text_styles.dart';
import 'package:safea/features/dashboard/presentation/pages/dashboard_user_tracking_screen.dart';
import 'package:safea/features/dashboard/presentation/pages/dashboard_user_done_screen.dart';

/// Dashboard home screen with swipe functionality
class DashboardHomeScreen extends StatefulWidget {
  const DashboardHomeScreen({super.key});

  @override
  State<DashboardHomeScreen> createState() => _DashboardHomeScreenState();
}

class _DashboardHomeScreenState extends State<DashboardHomeScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentPage = index;
          });
        },
        children: [
          _buildHomePage(),
          const DashboardUserTrackingScreen(),
          const DashboardUserDoneScreen(),
        ],
      ),
    );
  }

  Widget _buildHomePage() {
    return Container(
      decoration: const BoxDecoration(
        gradient: RadialGradient(
          center: Alignment.center,
          radius: 1.2,
          colors: [
            Color(0xFF00FF88), // Bright green center
            Color(0xFF00CC66), // Medium green
            Color(0xFF009944), // Darker green
            Color(0xFF001122), // Very dark background
          ],
          stops: [0.0, 0.3, 0.6, 1.0],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            const Spacer(flex: 2),
            _buildCenterContent(),
            const Spacer(flex: 3),
            _buildBottomNavigation(context),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildCenterContent() {
    return Column(
      children: [
        // Safea logo text
        Text(
          'safea',
          style: AppTextStyles.tagline.copyWith(
            fontSize: 24,
            color: Colors.white,
            fontWeight: FontWeight.w300,
          ),
        ),
        const SizedBox(height: 40),

        // Main status text
        Text(
          'Kein Notruf',
          style: AppTextStyles.tagline.copyWith(
            fontSize: 42,
            color: Colors.white,
            fontWeight: FontWeight.w300,
          ),
        ),
        const SizedBox(height: 8),

        // Subtitle
        Text(
          'zurzeit',
          style: AppTextStyles.bodyMedium.copyWith(
            fontSize: 18,
            color: Colors.white.withValues(alpha: 0.8),
            fontWeight: FontWeight.w300,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomNavigation(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // History/Clock icon
        IconButton(
          onPressed: () {
            // TODO: Navigate to history
          },
          icon: const Icon(Icons.history, color: Colors.white, size: 28),
        ),

        // Settings icon
        IconButton(
          onPressed: () {
            context.push(RouteNames.settings);
          },
          icon: const Icon(Icons.settings, color: Colors.white, size: 28),
        ),
      ],
    );
  }
}
