import 'package:flutter/material.dart';
import 'package:safea/core/theme/text_styles.dart';

/// Dashboard user done screen showing completed help
class DashboardUserDoneScreen extends StatelessWidget {
  const DashboardUserDoneScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.center,
            radius: 1.2,
            colors: [
              Color(0xFF00FF88), // Bright green center
              Color(0xFF00CC66), // Medium green
              Color(0xFF009944), // Darker green
              Color(0xFF001122), // Very dark background
            ],
            stops: [0.0, 0.3, 0.6, 1.0],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              const Spacer(),
              _buildUserProfile(),
              const Spacer(),
              _buildActionButton(),
              _buildNoTimeText(),
              const SizedBox(height: 100),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserProfile() {
    return Column(
      children: [
        // Profile image with oval border
        Container(
          width: 280,
          height: 350,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(140),
            border: Border.all(
              color: const Color(0xFFFF6B6B), // Red border
              width: 8,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(132),
            child: Image.asset(
              'assets/images/profile_placeholder.jpg', // You'll need to add this image
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                // Fallback if image doesn't exist
                return Container(
                  color: Colors.grey[300],
                  child: const Icon(
                    Icons.person,
                    size: 100,
                    color: Colors.grey,
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 24),
        
        // Name
        Text(
          'Magdalena',
          style: AppTextStyles.tagline.copyWith(
            fontSize: 32,
            color: Colors.white,
            fontWeight: FontWeight.w300,
          ),
        ),
        const SizedBox(height: 8),
        
        // Status text
        Text(
          'wird verfolgt',
          style: AppTextStyles.bodyMedium.copyWith(
            fontSize: 16,
            color: Colors.white.withValues(alpha: 0.8),
            fontWeight: FontWeight.w300,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 40),
      child: ElevatedButton(
        onPressed: () {
          // Handle help action
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 32),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          minimumSize: const Size(double.infinity, 50),
        ),
        child: Text(
          'Hilfe leisten - 0,5 km entfernt',
          style: AppTextStyles.bodyMedium.copyWith(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildNoTimeText() {
    return Padding(
      padding: const EdgeInsets.only(top: 24),
      child: Text(
        'Keine Zeit',
        style: AppTextStyles.bodyMedium.copyWith(
          fontSize: 16,
          color: Colors.white.withValues(alpha: 0.7),
          fontWeight: FontWeight.w300,
        ),
      ),
    );
  }
}
