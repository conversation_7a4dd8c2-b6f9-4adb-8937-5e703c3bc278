import 'package:flutter/material.dart';
import 'package:safea/core/theme/text_styles.dart';

/// Dashboard user tracking screen with timer display
class DashboardUserTrackingScreen extends StatelessWidget {
  const DashboardUserTrackingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.center,
            radius: 1.2,
            colors: [
              Color(0xFF00FF88), // Bright green center
              Color(0xFF00CC66), // Medium green
              Color(0xFF009944), // Darker green
              Color(0xFF001122), // Very dark background
            ],
            stops: [0.0, 0.3, 0.6, 1.0],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              const SizedBox(height: 60),
              _buildTimer(),
              const Spacer(),
              _buildUserProfile(),
              const Spacer(),
              _buildStatusText(),
              const SizedBox(height: 100),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTimer() {
    return Text(
      '00:00',
      style: AppTextStyles.tagline.copyWith(
        fontSize: 32,
        color: Colors.white,
        fontWeight: FontWeight.w300,
      ),
    );
  }

  Widget _buildUserProfile() {
    return Column(
      children: [
        // Profile image with oval border
        Container(
          width: 280,
          height: 350,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(140),
            border: Border.all(
              color: const Color(0xFFFF6B6B), // Red border
              width: 8,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(132),
            child: Image.asset(
              'assets/images/profile_placeholder.jpg', // You'll need to add this image
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                // Fallback if image doesn't exist
                return Container(
                  color: Colors.grey[300],
                  child: const Icon(
                    Icons.person,
                    size: 100,
                    color: Colors.grey,
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 24),
        
        // Name
        Text(
          'Magdalena',
          style: AppTextStyles.tagline.copyWith(
            fontSize: 32,
            color: Colors.white,
            fontWeight: FontWeight.w300,
          ),
        ),
        const SizedBox(height: 8),
        
        // Status text
        Text(
          'wird verfolgt',
          style: AppTextStyles.bodyMedium.copyWith(
            fontSize: 16,
            color: Colors.white.withValues(alpha: 0.8),
            fontWeight: FontWeight.w300,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusText() {
    return Text(
      'Aktiv geholfen',
      style: AppTextStyles.bodyMedium.copyWith(
        fontSize: 18,
        color: const Color(0xFF00FF88), // Green color
        fontWeight: FontWeight.w400,
      ),
    );
  }
}
