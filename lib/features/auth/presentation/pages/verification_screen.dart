import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/text_styles.dart';
import 'package:safea/core/storage/storage_providers.dart';
import 'package:safea/shared/widgets/app_scaffold.dart';
import 'package:safea/shared/widgets/app_button.dart';

/// Email/Phone verification screen with code input
class VerificationScreen extends ConsumerStatefulWidget {
  const VerificationScreen({super.key});

  @override
  ConsumerState<VerificationScreen> createState() => _VerificationScreenState();
}

class _VerificationScreenState extends ConsumerState<VerificationScreen>
    with TickerProviderStateMixin {
  final List<TextEditingController> _controllers = List.generate(
    4,
    (index) => TextEditingController(),
  );
  final List<FocusNode> _focusNodes = List.generate(4, (index) => FocusNode());

  late AnimationController _timerController;
  late Animation<double> _timerAnimation;
  int _remainingSeconds = 30;

  @override
  void initState() {
    super.initState();
    _setupTimer();
    _startTimer();
  }

  void _setupTimer() {
    _timerController = AnimationController(
      duration: const Duration(seconds: 30),
      vsync: this,
    );

    _timerAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(_timerController);

    _timerController.addListener(() {
      setState(() {
        _remainingSeconds = (30 * (1 - _timerController.value)).round();
      });
    });
  }

  void _startTimer() {
    _timerController.forward();
  }

  @override
  void dispose() {
    _timerController.dispose();
    for (final controller in _controllers) {
      controller.dispose();
    }
    for (final focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: AppColors.background,
      showBackButton: true,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 40),
              Text(
                'Bestätige dein Konto',
                style: AppTextStyles.tagline.copyWith(fontSize: 28),
              ),
              const SizedBox(height: 40),
              _buildCodeInput(),
              const SizedBox(height: 24),
              _buildTimer(),
              const SizedBox(height: 40),
              _buildDescription(),
              const Spacer(),
              _buildSkipButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCodeInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Füge den Code ein*', style: AppTextStyles.inputLabel),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(4, (index) {
            return Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: AppColors.inputBackground,
                border: Border.all(
                  color: _controllers[index].text.isNotEmpty
                      ? AppColors.primary
                      : AppColors.inputBorder,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: TextField(
                  controller: _controllers[index],
                  focusNode: _focusNodes[index],
                  textAlign: TextAlign.center,
                  style: AppTextStyles.codeInput,
                  keyboardType: TextInputType.number,
                  maxLength: 1,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    counterText: '',
                  ),
                  onChanged: (value) {
                    if (value.isNotEmpty && index < 3) {
                      _focusNodes[index + 1].requestFocus();
                    } else if (value.isEmpty && index > 0) {
                      _focusNodes[index - 1].requestFocus();
                    }

                    // Check if all fields are filled
                    if (_controllers.every((c) => c.text.isNotEmpty)) {
                      _handleVerification();
                    }

                    setState(() {});
                  },
                ),
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildTimer() {
    return AnimatedBuilder(
      animation: _timerAnimation,
      builder: (context, child) {
        return Text(
          '0:${_remainingSeconds.toString().padLeft(2, '0')} Code erneut schicken',
          style: AppTextStyles.bodyMedium.copyWith(
            color: _remainingSeconds > 0
                ? AppColors.success
                : AppColors.onBackgroundSecondary,
          ),
        );
      },
    );
  }

  Widget _buildDescription() {
    return Text(
      'Der Bestätigungscode wurde an deine\nE-Mail-Adresse gesendet.',
      style: AppTextStyles.bodyMedium.copyWith(
        color: AppColors.onBackgroundSecondary,
      ),
    );
  }

  Widget _buildSkipButton() {
    return AppButton(
      text: 'Setup starten',
      onPressed: _handleVerification,
      type: AppButtonType.primary,
      size: AppButtonSize.large,
    );
  }

  void _handleVerification() {
    // Check setup status and route accordingly
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        final needsSetup = ref.read(needsSetupProvider);
        if (needsSetup) {
          // If setup is needed, go to agreement screen first
          context.go(RouteNames.setup01);
        } else {
          // If setup is complete, go to dashboard
          context.go(RouteNames.dashboardHome);
        }
      }
    });
  }
}
