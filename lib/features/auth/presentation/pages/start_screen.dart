import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/text_styles.dart';
import 'package:safea/core/theme/responsive_extensions.dart';
import 'package:safea/shared/widgets/app_button.dart';
import 'package:safea/shared/widgets/app_scaffold.dart';
import 'package:safea/shared/widgets/wavy_glow_background.dart';

class StartScreen extends StatelessWidget {
  const StartScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: AppColors.background,
      safeArea: false,
      body: GradientBackground(
        colors: AppColors.backgroundGradient,
        child: WavyGlowBackground(
          intensity: 0.6, // Subtle background effect
          child: SafeArea(
            child: Padding(
              padding: context.screenPaddingHorizontal,
              child: SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: context.availableHeight - context.spacingXxxl,
                  ),
                  child: Column(
                    children: [
                      SizedBox(height: context.spacingXl),
                      _buildHeader(context),
                      SizedBox(height: context.spacingXl),
                      _buildContent(context),
                      SizedBox(height: context.spacingXl),
                      _buildActions(context),
                      SizedBox(height: context.spacingXl),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        Text(
          'Ein Klick.\nSofort helfen.\nSicherheit bieten.',
          style: AppTextStyles.responsiveTagline(context),
          textAlign: TextAlign.left,
        ),
        SizedBox(height: context.spacingLg),
        // Use the wavy glow effect component
        WavyGlowEffect(
          width: context.responsiveSpacing(200),
          height: context.responsiveSpacing(300),
          showLogo: true,
          logoText: 'safea',
          logoStyle: AppTextStyles.logo.copyWith(
            color: AppColors.onBackground,
            fontSize: context.responsiveFont(16),
          ),
          intensity: 1,
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Öffne die App, um Menschen in kritischen\n'
          'Momenten zu helfen. Erkenne Gefahr,\n'
          'greife ein und biete Unterstützung –\n'
          'schnell und unkompliziert.',
          style: AppTextStyles.subtitle.copyWith(
            fontSize: context.responsiveFont(16),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildActions(BuildContext context) {
    return Column(
      children: [
        AppButton(
          text: 'Jetzt als Privatperson anmelden',
          onPressed: () => context.push(RouteNames.signUp),
          size: AppButtonSize.large,
        ),
        SizedBox(height: context.spacingMd),
        GestureDetector(
          onTap: () => context.push(RouteNames.login),
          child: Text(
            'Bereits registriert? Anmelden',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.primary,
              decoration: TextDecoration.underline,
              fontSize: context.responsiveFont(14),
            ),
          ),
        ),
        SizedBox(height: context.spacingMd),
        AppButton(
          text: 'Als Unternehmen',
          onPressed: () {
            context.push(RouteNames.signUp);
          },
          type: AppButtonType.text,
        ),
      ],
    );
  }
}
