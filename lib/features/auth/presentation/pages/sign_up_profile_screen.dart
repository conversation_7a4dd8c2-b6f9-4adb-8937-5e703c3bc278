import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/text_styles.dart';
import 'package:safea/shared/widgets/app_scaffold.dart';
import 'package:safea/shared/widgets/app_button.dart';

/// Profile image upload screen
class SignUpProfileScreen extends StatefulWidget {
  const SignUpProfileScreen({super.key});

  @override
  State<SignUpProfileScreen> createState() => _SignUpProfileScreenState();
}

class _SignUpProfileScreenState extends State<SignUpProfileScreen> {
  bool _hasProfileImage = false;

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: AppColors.background,
      showBackButton: true,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 40),
              Text(
                'Gib deinem\nProfil ein Gesicht',
                style: AppTextStyles.tagline.copyWith(fontSize: 28),
              ),
              const SizedBox(height: 60),
              Expanded(child: _buildProfileSection()),
              _buildBottomSection(),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileSection() {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(minHeight: constraints.maxHeight),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildGlowEffect(),
                const SizedBox(height: 40),
                GestureDetector(
                  onTap: _handleImageUpload,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.border),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Text(
                      'Bild hochladen',
                      style: AppTextStyles.buttonSecondary.copyWith(
                        color: AppColors.onBackground,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Verwende ein gut erkennbares Bild\ndeines Gesichts.',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.onBackgroundSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildGlowEffect() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Scale the glow effect based on available space
        final maxWidth = constraints.maxWidth;
        final scale = (maxWidth / 300).clamp(0.6, 1.0);

        return SizedBox(
          height: 280 * scale,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Outer glow
              Container(
                width: 200 * scale,
                height: 280 * scale,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100 * scale),
                  gradient: RadialGradient(
                    colors: [
                      AppColors.primary.withOpacity(0.3),
                      AppColors.primary.withOpacity(0.1),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
              ),
              // Middle glow
              Container(
                width: 150 * scale,
                height: 210 * scale,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(75 * scale),
                  gradient: RadialGradient(
                    colors: [
                      AppColors.primary.withOpacity(0.5),
                      AppColors.primary.withOpacity(0.2),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.6, 1.0],
                  ),
                ),
              ),
              // Inner glow
              Container(
                width: 100 * scale,
                height: 140 * scale,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(50 * scale),
                  gradient: RadialGradient(
                    colors: [
                      AppColors.primary.withOpacity(0.8),
                      AppColors.primary.withOpacity(0.4),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.5, 1.0],
                  ),
                ),
              ),
              // Core oval with upload icon
              Container(
                width: 80 * scale,
                height: 120 * scale,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(40 * scale),
                  color: AppColors.background,
                  border: Border.all(
                    color: AppColors.primary.withOpacity(0.6),
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Icon(
                    Icons.cloud_upload_outlined,
                    color: AppColors.onBackgroundSecondary,
                    size: 32 * scale,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBottomSection() {
    return Column(
      children: [
        Text(
          'Menschen in Not sehen dein Bild,\num dich besser zu erkennen.',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.onBackgroundSecondary,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        AppButton(
          text: 'Bild als Profilbild festlegen',
          onPressed: _handleSetProfileImage,
          type: AppButtonType.primary,
          size: AppButtonSize.large,
        ),
      ],
    );
  }

  void _handleImageUpload() {
    // For MVP, simulate image upload
    setState(() {
      _hasProfileImage = true;
    });
  }

  void _handleSetProfileImage() {
    // For MVP, always proceed to verification
    context.push(RouteNames.verification);
  }
}
