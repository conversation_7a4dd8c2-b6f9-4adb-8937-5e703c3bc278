import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/text_styles.dart';
import 'package:safea/shared/widgets/app_scaffold.dart';
import 'package:safea/shared/widgets/app_button.dart';
import 'package:safea/shared/widgets/app_text_field.dart';

/// Login screen
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Pre-fill with mock data for MVP
    _emailController.text = '<EMAIL>';
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: AppColors.background,
      showBackButton: true,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(child: _buildForm()),
            _buildBottomSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 2,
              decoration: BoxDecoration(
                color: AppColors.onBackgroundTertiary,
                borderRadius: BorderRadius.circular(1),
              ),
            ),
          ),
          const SizedBox(width: 16),
          GestureDetector(
            onTap: () => context.pushReplacement(RouteNames.signUp),
            child: Text(
              'Mitmachen',
              style: AppTextStyles.titleLarge.copyWith(
                color: AppColors.onBackgroundTertiary,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Container(
              height: 2,
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(1),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Text(
            'Anmelden',
            style: AppTextStyles.titleLarge.copyWith(
              color: AppColors.onBackground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 40),
            AppTextField(
              controller: _emailController,
              label: 'Deine E-Mail-Adresse*',
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'E-Mail-Adresse ist erforderlich';
                }
                return null;
              },
            ),
            const SizedBox(height: 200), // Spacer to match design
          ],
        ),
      ),
    );
  }

  Widget _buildBottomSection() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Text(
            'Öffne die App, um Menschen in kritischen\nMomenten zu helfen. Erkenne Gefahr,\ngreife ein und biete Unterstützung –\nschnell und unkompliziert.',
            style: AppTextStyles.subtitle,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          AppButton(
            text: 'Jetzt als Privatperson anmelden',
            onPressed: _handleLogin,
            type: AppButtonType.primary,
            size: AppButtonSize.large,
          ),
          const SizedBox(height: 16),
          AppButton(
            text: 'Als Unternehmen',
            onPressed: () {
              // TODO: Implement company login
              _handleLogin();
            },
            type: AppButtonType.text,
            size: AppButtonSize.medium,
          ),
        ],
      ),
    );
  }

  void _handleLogin() {
    // For MVP, always proceed to verification
    context.push(RouteNames.verification);
  }
}
