import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/text_styles.dart';
import 'package:safea/shared/widgets/app_scaffold.dart';
import 'package:safea/shared/widgets/app_button.dart';
import 'package:safea/shared/widgets/app_text_field.dart';

/// Sign up screen with form fields
class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _streetController = TextEditingController();
  final _postalCodeController = TextEditingController();
  final _cityController = TextEditingController();
  final _countryController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Pre-fill with mock data for MVP
    _emailController.text = '<EMAIL>';
    _firstNameController.text = 'Max';
    _lastNameController.text = 'Mustermann';
    _streetController.text = 'Maximilian-Straße 19a';
    _postalCodeController.text = '09192';
    _cityController.text = 'München';
    _countryController.text = 'Deutschland';
  }

  @override
  void dispose() {
    _emailController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _streetController.dispose();
    _postalCodeController.dispose();
    _cityController.dispose();
    _countryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: AppColors.background,
      showBackButton: true,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(child: _buildForm()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 2,
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(1),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Text(
            'Mitmachen',
            style: AppTextStyles.titleLarge.copyWith(
              color: AppColors.onBackground,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Container(
              height: 2,
              decoration: BoxDecoration(
                color: AppColors.onBackgroundTertiary,
                borderRadius: BorderRadius.circular(1),
              ),
            ),
          ),
          const SizedBox(width: 16),
          GestureDetector(
            onTap: () => context.pushReplacement(RouteNames.login),
            child: Text(
              'Anmelden',
              style: AppTextStyles.titleLarge.copyWith(
                color: AppColors.onBackgroundTertiary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppTextField(
              controller: _emailController,
              label: 'Deine E-Mail-Adresse*',
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'E-Mail-Adresse ist erforderlich';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            AppTextField(
              controller: _firstNameController,
              label: 'Dein Vorname*',
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Vorname ist erforderlich';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            AppTextField(
              controller: _lastNameController,
              label: 'Dein Nachname*',
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Nachname ist erforderlich';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            AppTextField(
              controller: _streetController,
              label: 'Deine Straße*',
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Straße ist erforderlich';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            AppTextField(
              controller: _postalCodeController,
              label: 'Postleitzahl*',
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Postleitzahl ist erforderlich';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            AppTextField(
              controller: _cityController,
              label: 'Stadtname*',
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Stadtname ist erforderlich';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            AppTextField(
              controller: _countryController,
              label: 'Land*',
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Land ist erforderlich';
                }
                return null;
              },
            ),
            const SizedBox(height: 32),
            AppButton(
              text: 'Jetzt Verifizieren',
              onPressed: _handleSignUp,
              type: AppButtonType.primary,
              size: AppButtonSize.large,
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  void _handleSignUp() {
    // For MVP, always proceed regardless of validation
    context.push(RouteNames.signUpProfile);
  }
}
