import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../data/repositories/user_repository_impl.dart';
import '../entities/user.dart';
import '../repositories/user_repository.dart';

part 'get_users_usecase.g.dart';

class GetUsersUseCase {
  const GetUsersUseCase(this._repository);

  final UserRepository _repository;

  Future<List<User>> call() async {
    return _repository.getUsers();
  }
}

@riverpod
GetUsersUseCase getUsersUseCase(GetUsersUseCaseRef ref) {
  return GetUsersUseCase(ref.watch(userRepositoryProvider));
}
