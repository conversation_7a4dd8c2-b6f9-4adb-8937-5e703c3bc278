// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_users_usecase.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$getUsersUseCaseHash() => r'71955967ca8dd465f93387e95de9e3923d07d7b8';

/// See also [getUsersUseCase].
@ProviderFor(getUsersUseCase)
final getUsersUseCaseProvider = AutoDisposeProvider<GetUsersUseCase>.internal(
  getUsersUseCase,
  name: r'getUsersUseCaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getUsersUseCaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetUsersUseCaseRef = AutoDisposeProviderRef<GetUsersUseCase>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
