import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../domain/entities/user.dart';
import '../../domain/usecases/get_users_usecase.dart';

part 'user_list_provider.g.dart';

@riverpod
class UserListNotifier extends _$UserListNotifier {
  @override
  Future<List<User>> build() async {
    return _getUsers();
  }

  Future<List<User>> _getUsers() async {
    final useCase = ref.read(getUsersUseCaseProvider);
    return useCase.call();
  }

  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _getUsers());
  }
}
