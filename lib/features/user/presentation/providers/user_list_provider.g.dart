// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_list_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userListNotifierHash() => r'580e3158ce660f54baa84aa1b19632440d757676';

/// See also [UserListNotifier].
@ProviderFor(UserListNotifier)
final userListNotifierProvider =
    AutoDisposeAsyncNotifierProvider<UserListNotifier, List<User>>.internal(
      UserListNotifier.new,
      name: r'userListNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userListNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$UserListNotifier = AutoDisposeAsyncNotifier<List<User>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
