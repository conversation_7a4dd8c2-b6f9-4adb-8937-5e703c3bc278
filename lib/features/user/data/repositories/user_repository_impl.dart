import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../domain/entities/user.dart';
import '../../domain/repositories/user_repository.dart';
import '../datasources/user_remote_datasource.dart';
import '../models/user_model.dart';

part 'user_repository_impl.g.dart';

class UserRepositoryImpl implements UserRepository {
  const UserRepositoryImpl(this._remoteDataSource);

  final UserRemoteDataSource _remoteDataSource;

  @override
  Future<List<User>> getUsers() async {
    final userModels = await _remoteDataSource.getUsers();
    return userModels.map((model) => model.toEntity()).toList();
  }

  @override
  Future<User> getUserById(int id) async {
    final userModel = await _remoteDataSource.getUserById(id);
    return userModel.toEntity();
  }

  @override
  Future<User> createUser(User user) async {
    final userModel = _userToModel(user);
    final createdUserModel = await _remoteDataSource.createUser(userModel);
    return createdUserModel.toEntity();
  }

  @override
  Future<User> updateUser(User user) async {
    final userModel = _userToModel(user);
    final updatedUserModel = await _remoteDataSource.updateUser(userModel);
    return updatedUserModel.toEntity();
  }

  @override
  Future<void> deleteUser(int id) async {
    await _remoteDataSource.deleteUser(id);
  }

  UserModel _userToModel(User user) {
    return UserModel(
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      website: user.website,
      address: AddressModel(
        street: user.address.street,
        suite: user.address.suite,
        city: user.address.city,
        zipcode: user.address.zipcode,
        geo: GeoModel(
          lat: user.address.geo.lat,
          lng: user.address.geo.lng,
        ),
      ),
      company: CompanyModel(
        name: user.company.name,
        catchPhrase: user.company.catchPhrase,
        bs: user.company.bs,
      ),
    );
  }
}

@riverpod
UserRepository userRepository(UserRepositoryRef ref) {
  return UserRepositoryImpl(ref.watch(userRemoteDataSourceProvider));
}
