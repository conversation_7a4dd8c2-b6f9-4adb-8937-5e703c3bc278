import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/network/api_client.dart';
import '../models/user_model.dart';

part 'user_remote_datasource.g.dart';

abstract class UserRemoteDataSource {
  Future<List<UserModel>> getUsers();
  Future<UserModel> getUserById(int id);
  Future<UserModel> createUser(UserModel user);
  Future<UserModel> updateUser(UserModel user);
  Future<void> deleteUser(int id);
}

class UserRemoteDataSourceImpl implements UserRemoteDataSource {
  const UserRemoteDataSourceImpl(this._apiClient);

  final ApiClient _apiClient;

  @override
  Future<List<UserModel>> getUsers() async {
    final response = await _apiClient.get<List<dynamic>>('/users');
    
    if (response.data == null) {
      throw Exception('No data received');
    }

    return response.data!
        .map((json) => UserModel.fromJson(json as Map<String, dynamic>))
        .toList();
  }

  @override
  Future<UserModel> getUserById(int id) async {
    final response = await _apiClient.get<Map<String, dynamic>>('/users/$id');
    
    if (response.data == null) {
      throw Exception('No data received');
    }

    return UserModel.fromJson(response.data!);
  }

  @override
  Future<UserModel> createUser(UserModel user) async {
    final response = await _apiClient.post<Map<String, dynamic>>(
      '/users',
      data: user.toJson(),
    );
    
    if (response.data == null) {
      throw Exception('No data received');
    }

    return UserModel.fromJson(response.data!);
  }

  @override
  Future<UserModel> updateUser(UserModel user) async {
    final response = await _apiClient.put<Map<String, dynamic>>(
      '/users/${user.id}',
      data: user.toJson(),
    );
    
    if (response.data == null) {
      throw Exception('No data received');
    }

    return UserModel.fromJson(response.data!);
  }

  @override
  Future<void> deleteUser(int id) async {
    await _apiClient.delete('/users/$id');
  }
}

@riverpod
UserRemoteDataSource userRemoteDataSource(UserRemoteDataSourceRef ref) {
  return UserRemoteDataSourceImpl(ref.watch(apiClientProvider));
}
