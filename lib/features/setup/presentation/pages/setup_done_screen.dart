import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/text_styles.dart';
import 'package:safea/core/storage/storage_providers.dart';
import 'package:safea/shared/widgets/app_scaffold.dart';
import 'package:safea/shared/widgets/app_button.dart';

/// Setup completion screen
class SetupDoneScreen extends ConsumerWidget {
  const SetupDoneScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppScaffold(
      backgroundColor: AppColors.background,
      showBackButton: false,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              const Spacer(),
              _buildSuccessIcon(),
              const SizedBox(height: 40),
              _buildTitle(),
              const SizedBox(height: 16),
              _buildDescription(),
              const Spacer(),
              _buildActions(context, ref),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSuccessIcon() {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: AppColors.success,
        shape: BoxShape.circle,
      ),
      child: const Icon(
        Icons.check,
        color: Colors.white,
        size: 60,
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      'Setup abgeschlossen!',
      style: AppTextStyles.tagline.copyWith(fontSize: 28),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildDescription() {
    return Text(
      'Dein Konto ist jetzt vollständig eingerichtet und bereit für die Nutzung. Du kannst jetzt anderen Menschen in Notfällen helfen.',
      style: AppTextStyles.bodyMedium.copyWith(
        color: AppColors.onBackgroundSecondary,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildActions(BuildContext context, WidgetRef ref) {
    return AppButton(
      text: 'Zur App',
      onPressed: () => _handleComplete(context, ref),
      type: AppButtonType.primary,
      size: AppButtonSize.large,
    );
  }

  Future<void> _handleComplete(BuildContext context, WidgetRef ref) async {
    // Mark setup as completed
    await ref.read(setupStatusProvider.notifier).setCompleted(true);
    
    // Navigate to dashboard or main app
    if (context.mounted) {
      context.go(RouteNames.dashboardHome);
    }
  }
}
