import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/text_styles.dart';
import 'package:safea/core/storage/storage_providers.dart';
import 'package:safea/shared/widgets/app_scaffold.dart';
import 'package:safea/shared/widgets/app_button.dart';

/// Setup01 screen with agreement points
class Setup01Screen extends ConsumerStatefulWidget {
  const Setup01Screen({super.key});

  @override
  ConsumerState<Setup01Screen> createState() => _Setup01ScreenState();
}

class _Setup01ScreenState extends ConsumerState<Setup01Screen> {
  final List<bool> _agreements = List.filled(5, false);

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: AppColors.background,
      showBackButton: true,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              Text(
                'Unsere Vereinbarung',
                style: AppTextStyles.tagline.copyWith(fontSize: 28),
              ),
              const SizedBox(height: 40),
              Expanded(child: _buildAgreementList()),
              _buildActions(),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAgreementList() {
    final agreements = [
      'Jede Minute zählt!\nMit deiner Zeit leistest du echte, greifbare Unterstützung für Menschen in schwierigen Situationen, die dringend Hilfe brauchen.',
      'Sei nur dann zur Hilfe bereit, wenn du dich selbst sicher fühlst. So schützt du dich und andere effektiv.',
      'Wir bringen Hilfesuchende und Helfer zusammen, bieten aber keinen Polizeischutz oder Sicherheitsdienstleistungen an.',
      'Empathie und Respekt machen dich zur starken Stütze – sei die Schulter, auf die Menschen in Not vertrauen können.',
      'Gewalt bringt keine Lösung. Informiere bei ernsten Vorfällen Polizei oder Rettungsdienst vor Ort. Bleibe selbst ruhig und friedvoll.',
    ];

    return ListView.builder(
      itemCount: agreements.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 24),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    _agreements[index] = !_agreements[index];
                  });
                },
                child: Container(
                  width: 20,
                  height: 20,
                  margin: const EdgeInsets.only(top: 2),
                  decoration: BoxDecoration(
                    color: _agreements[index]
                        ? AppColors.primary
                        : Colors.transparent,
                    border: Border.all(
                      color: _agreements[index]
                          ? AppColors.primary
                          : AppColors.border,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: _agreements[index]
                      ? const Icon(
                          Icons.check,
                          color: AppColors.background,
                          size: 14,
                        )
                      : null,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  agreements[index],
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.onBackground,
                    height: 1.5,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildActions() {
    final allAgreed = _agreements.every((agreed) => agreed);

    return Column(
      children: [
        AppButton(
          text: 'Bestätige unsere Vereinbarung',
          onPressed: allAgreed ? _handleConfirm : null,
          type: AppButtonType.primary,
          size: AppButtonSize.large,
          isEnabled: allAgreed,
        ),
        const SizedBox(height: 16),
        GestureDetector(
          onTap: () => context.push(RouteNames.setup01Agb),
          child: Text(
            'Unsere AGB\'s',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.onBackgroundSecondary,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleConfirm() async {
    // Mark agreements as accepted
    await ref.read(agreementsStatusProvider.notifier).setAccepted(true);

    // Navigate directly to setup start screen (120 seconds page)
    if (mounted) {
      context.push(RouteNames.setupStart);
    }
  }
}
