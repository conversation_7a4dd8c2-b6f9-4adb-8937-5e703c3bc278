import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/text_styles.dart';
import 'package:safea/shared/widgets/app_scaffold.dart';
import 'package:safea/shared/widgets/app_button.dart';

/// Available times setup screen with time picker
class SetupAvailableTimesScreen extends ConsumerStatefulWidget {
  const SetupAvailableTimesScreen({super.key});

  @override
  ConsumerState<SetupAvailableTimesScreen> createState() =>
      _SetupAvailableTimesScreenState();
}

class _SetupAvailableTimesScreenState
    extends ConsumerState<SetupAvailableTimesScreen> {
  String selectedDay = 'Montag';
  TimeOfDay startTime = const TimeOfDay(hour: 0, minute: 0);
  TimeOfDay endTime = const TimeOfDay(hour: 0, minute: 0);

  final List<String> weekdays = [
    'Sonntag',
    'Montag',
    'Dienstag',
    'Mittwoch',
    'Donnerstag',
    'Freitag',
    'Samstag',
  ];

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: AppColors.background,
      showBackButton: true,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              Text(
                'Deine Verfügbarkeit',
                style: AppTextStyles.tagline.copyWith(fontSize: 28),
              ),
              const SizedBox(height: 40),
              _buildDaySelector(),
              const SizedBox(height: 40),
              Expanded(child: _buildTimeSelector()),
              _buildBottomText(),
              const SizedBox(height: 24),
              _buildActions(context),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDaySelector() {
    return SizedBox(
      height: 50,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: weekdays.length,
        itemBuilder: (context, index) {
          final day = weekdays[index];
          final isSelected = day == selectedDay;

          return GestureDetector(
            onTap: () {
              setState(() {
                selectedDay = day;
              });
            },
            child: Container(
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppColors.primary
                    : AppColors.surfaceVariant,
                borderRadius: BorderRadius.circular(25),
              ),
              child: Center(
                child: Text(
                  day,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: isSelected
                        ? Colors.white
                        : AppColors.onSurfaceVariant,
                    fontWeight: isSelected
                        ? FontWeight.w600
                        : FontWeight.normal,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTimeSelector() {
    return Column(
      children: [
        const SizedBox(height: 20),
        _buildTimeDisplay(startTime, 'von'),
        const SizedBox(height: 24),
        Text(
          'bis',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.onBackgroundSecondary,
          ),
        ),
        const SizedBox(height: 24),
        _buildTimeDisplay(endTime, 'bis'),
        const SizedBox(height: 32),
        _buildTimeIndicators(),
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildTimeDisplay(TimeOfDay time, String label) {
    return GestureDetector(
      onTap: () => _selectTime(label == 'von'),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 20),
        decoration: BoxDecoration(
          color: AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(16),
        ),
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(
            '${time.hour.toString().padLeft(2, '0')}:'
            '${time.minute.toString().padLeft(2, '0')}',
            style: AppTextStyles.tagline.copyWith(
              fontSize: 48,
              color: AppColors.onSurfaceVariant,
              fontWeight: FontWeight.w300,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTimeIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: const BoxDecoration(
            color: AppColors.success,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Container(
          width: 8,
          height: 8,
          decoration: const BoxDecoration(
            color: AppColors.success,
            shape: BoxShape.circle,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomText() {
    return Text(
      'Fülle alle Tage mit Zeiten aus, um dein Setup zu beenden.',
      style: AppTextStyles.bodyMedium.copyWith(
        color: AppColors.onBackgroundSecondary,
      ),
    );
  }

  Widget _buildActions(BuildContext context) {
    return Column(
      children: [
        AppButton(
          text: 'Zeit setzen',
          onPressed: () => _handleSetTime(context),
          type: AppButtonType.primary,
          size: AppButtonSize.large,
        ),
        const SizedBox(height: 16),
        AppButton(
          text: 'Keine Zeit',
          onPressed: () => _handleNoTime(context),
          type: AppButtonType.text,
          size: AppButtonSize.medium,
        ),
      ],
    );
  }

  Future<void> _selectTime(bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStartTime ? startTime : endTime,
    );

    if (picked != null) {
      setState(() {
        if (isStartTime) {
          startTime = picked;
        } else {
          endTime = picked;
        }
      });
    }
  }

  void _handleSetTime(BuildContext context) {
    // For now, just proceed to setup done (completion screen)
    context.push(RouteNames.setupDone);
  }

  void _handleNoTime(BuildContext context) {
    // Clear times for this day and proceed
    setState(() {
      startTime = const TimeOfDay(hour: 0, minute: 0);
      endTime = const TimeOfDay(hour: 0, minute: 0);
    });
    context.push(RouteNames.setupDone);
  }
}
