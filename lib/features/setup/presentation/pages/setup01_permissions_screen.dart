import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/text_styles.dart';
import 'package:safea/shared/widgets/app_scaffold.dart';

/// Setup01 permissions agreement screen
class Setup01PermissionsScreen extends StatelessWidget {
  const Setup01PermissionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: const Color(0xFF1A1A1A), // Dark background
      showBackButton: true,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              
              // Title
              Text(
                'Unsere Vereinbarung',
                style: AppTextStyles.headlineMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Agreement points
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildAgreementPoint(
                        'Jede Minute zählt!',
                        'Mit deiner Zeit leistest du echte, greifbare Unterstützung für Menschen in schwierigen Situationen, die dringend Hilfe brauchen.',
                      ),
                      const SizedBox(height: 24),
                      
                      _buildAgreementPoint(
                        'Sei nur dann zur Hilfe bereit, wenn du dich selbst sicher fühlst.',
                        'So schützt du dich und andere effektiv.',
                      ),
                      const SizedBox(height: 24),
                      
                      _buildAgreementPoint(
                        'Wir bringen Hilfesuchende und Helfer zusammen.',
                        'bieten aber keinen Polizeischutz oder Sicherheitsdienstleistungen an.',
                      ),
                      const SizedBox(height: 24),
                      
                      _buildAgreementPoint(
                        'Empathie und Respekt machen dich zur starken Stütze',
                        '– sei die Schulter, auf die Menschen in Not vertrauen können.',
                      ),
                      const SizedBox(height: 24),
                      
                      _buildAgreementPoint(
                        'Gewalt bringt keine Lösung.',
                        'Informiere bei ernsten Vorfällen Polizei oder Rettungsdienst vor Ort. Bleibe selbst ruhig und friedvoll.',
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Bottom buttons
              Row(
                children: [
                  // Main agreement button
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: () {
                        context.push(RouteNames.setupStart);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: Colors.black,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                      ),
                      child: Text(
                        'Bestätige unsere Vereinbarung',
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // AGB button
                  GestureDetector(
                    onTap: () {
                      context.push(RouteNames.setup01Agb);
                    },
                    child: Text(
                      'Unsere AGB\'s',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Colors.white,
                        decoration: TextDecoration.underline,
                        decorationColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAgreementPoint(String title, String description) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Green checkmark
        Container(
          width: 20,
          height: 20,
          margin: const EdgeInsets.only(top: 2),
          decoration: const BoxDecoration(
            color: AppColors.primary,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.check,
            color: Colors.white,
            size: 14,
          ),
        ),
        
        const SizedBox(width: 16),
        
        // Text content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodyLarge.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.white.withValues(alpha: 0.8),
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
