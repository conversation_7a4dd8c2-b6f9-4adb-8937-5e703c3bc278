import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/text_styles.dart';
import 'package:safea/shared/widgets/app_button.dart';

/// Start setup screen with 120 seconds design (no counter)
class SetupStartScreen extends ConsumerWidget {
  const SetupStartScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.center,
            radius: 1.5,
            colors: [
              Color(0xFF00FF88), // Bright green center
              Color(0xFF00CC66), // Medium green
              Color(0xFF009944), // Darker green
              Color(0xFF001122), // Very dark background
            ],
            stops: [0.0, 0.3, 0.6, 1.0],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                const SizedBox(height: 60),
                _buildTitle(),
                const Spacer(),
                _buildCenterCircle(),
                const Spacer(),
                _buildStartButton(context),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Column(
      children: [
        Text(
          'Werde in',
          style: AppTextStyles.tagline.copyWith(
            fontSize: 32,
            color: Colors.white,
            fontWeight: FontWeight.w300,
          ),
        ),
        Text(
          '120 Sekunden',
          style: AppTextStyles.tagline.copyWith(
            fontSize: 32,
            color: Colors.white,
            fontWeight: FontWeight.w300,
          ),
        ),
        Text(
          'zur Unterstützung.',
          style: AppTextStyles.tagline.copyWith(
            fontSize: 32,
            color: Colors.white,
            fontWeight: FontWeight.w300,
          ),
        ),
      ],
    );
  }

  Widget _buildCenterCircle() {
    return Container(
      width: 300,
      height: 300,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          center: Alignment.center,
          radius: 0.8,
          colors: [
            Colors.transparent,
            Colors.black.withValues(alpha: 0.3),
            Colors.black.withValues(alpha: 0.6),
            Colors.black.withValues(alpha: 0.9),
          ],
          stops: const [0.0, 0.4, 0.7, 1.0],
        ),
      ),
      child: Center(
        child: Text(
          'safea',
          style: AppTextStyles.tagline.copyWith(
            fontSize: 32,
            color: Colors.white,
            fontWeight: FontWeight.w300,
          ),
        ),
      ),
    );
  }

  Widget _buildStartButton(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
      ),
      child: AppButton(
        text: 'Setup starten und loslegen',
        onPressed: () => _handleStartSetup(context),
        type: AppButtonType.primary,
        size: AppButtonSize.large,
      ),
    );
  }

  void _handleStartSetup(BuildContext context) {
    // Navigate to tracking setup
    context.push(RouteNames.setupTracking);
  }
}
