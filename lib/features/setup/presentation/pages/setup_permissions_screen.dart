import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/text_styles.dart';
import 'package:safea/core/services/permissions_service.dart';
import 'package:safea/core/storage/storage_providers.dart';
import 'package:safea/shared/widgets/app_scaffold.dart';
import 'package:safea/shared/widgets/app_button.dart';

/// Permissions setup screen for requesting required permissions
class SetupPermissionsScreen extends ConsumerStatefulWidget {
  const SetupPermissionsScreen({super.key});

  @override
  ConsumerState<SetupPermissionsScreen> createState() =>
      _SetupPermissionsScreenState();
}

class _SetupPermissionsScreenState
    extends ConsumerState<SetupPermissionsScreen> {
  bool _isRequestingPermissions = false;
  Map<Permission, PermissionStatus> _permissionStatuses = {};

  @override
  void initState() {
    super.initState();
    _checkCurrentPermissions();
  }

  Future<void> _checkCurrentPermissions() async {
    final permissions = [
      Permission.location,
      Permission.notification,
      Permission.camera,
      Permission.microphone,
      Permission.phone,
    ];

    final statuses = <Permission, PermissionStatus>{};
    for (final permission in permissions) {
      statuses[permission] = await permission.status;
    }

    setState(() {
      _permissionStatuses = statuses;
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: AppColors.background,
      showBackButton: true,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              Text(
                'Berechtigungen erforderlich',
                style: AppTextStyles.tagline.copyWith(fontSize: 28),
              ),
              const SizedBox(height: 16),
              Text(
                'Für die optimale Nutzung der App benötigen wir folgende Berechtigungen:',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.onBackgroundSecondary,
                ),
              ),
              const SizedBox(height: 40),
              Expanded(child: _buildPermissionsList()),
              _buildActions(),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPermissionsList() {
    final permissions = [
      {
        'permission': Permission.location,
        'title': 'Standort',
        'description': 'Für die Standortbestimmung in Notfällen',
        'icon': Icons.location_on,
      },
      {
        'permission': Permission.notification,
        'title': 'Benachrichtigungen',
        'description': 'Für wichtige Mitteilungen und Alarme',
        'icon': Icons.notifications,
      },
      {
        'permission': Permission.camera,
        'title': 'Kamera',
        'description': 'Für Foto- und Videoaufnahmen in Notfällen',
        'icon': Icons.camera_alt,
      },
      {
        'permission': Permission.microphone,
        'title': 'Mikrofon',
        'description': 'Für Audioaufnahmen und Sprachnachrichten',
        'icon': Icons.mic,
      },
      {
        'permission': Permission.phone,
        'title': 'Telefon',
        'description': 'Für Notrufe und direkte Kommunikation',
        'icon': Icons.phone,
      },
    ];

    return ListView.builder(
      itemCount: permissions.length,
      itemBuilder: (context, index) {
        final permissionData = permissions[index];
        final permission = permissionData['permission'] as Permission;
        final status = _permissionStatuses[permission];
        final isGranted = status?.isGranted ?? false;

        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surfaceVariant,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isGranted ? AppColors.success : AppColors.border,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: isGranted ? AppColors.success : AppColors.primary,
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(
                  permissionData['icon'] as IconData,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      permissionData['title'] as String,
                      style: AppTextStyles.titleMedium,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      permissionData['description'] as String,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.onBackgroundSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                isGranted ? Icons.check_circle : Icons.circle_outlined,
                color: isGranted ? AppColors.success : AppColors.border,
                size: 24,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildActions() {
    final allGranted = _permissionStatuses.values.every(
      (status) => status.isGranted,
    );

    return Column(
      children: [
        AppButton(
          text: _isRequestingPermissions
              ? 'Berechtigungen werden angefragt...'
              : allGranted
              ? 'Weiter'
              : 'Berechtigungen erteilen',
          onPressed: _isRequestingPermissions
              ? null
              : allGranted
              ? _handleContinue
              : _handleRequestPermissions,
          type: AppButtonType.primary,
          size: AppButtonSize.large,
          isEnabled: !_isRequestingPermissions,
        ),
        if (!allGranted) ...[
          const SizedBox(height: 16),
          AppButton(
            text: 'Einstellungen öffnen',
            onPressed: _handleOpenSettings,
            type: AppButtonType.text,
            size: AppButtonSize.medium,
          ),
        ],
      ],
    );
  }

  Future<void> _handleRequestPermissions() async {
    setState(() {
      _isRequestingPermissions = true;
    });

    try {
      final results = await PermissionsService.requestAllRequiredPermissions();
      setState(() {
        _permissionStatuses = results;
      });

      // Check if all permissions are granted
      final allGranted = results.values.every((status) => status.isGranted);
      if (allGranted) {
        await ref.read(permissionsStatusProvider.notifier).setGranted(true);
        _handleContinue();
      }
    } catch (e) {
      // Handle error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler beim Anfordern der Berechtigungen: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() {
        _isRequestingPermissions = false;
      });
    }
  }

  Future<void> _handleOpenSettings() async {
    await PermissionsService.openSettings();
    // Recheck permissions when user returns
    await _checkCurrentPermissions();
  }

  void _handleContinue() {
    // Navigate to next setup step or complete setup
    context.go(RouteNames.setupDone);
  }
}
