import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/text_styles.dart';
import 'package:safea/core/storage/storage_providers.dart';
import 'package:safea/shared/widgets/app_button.dart';

/// Setup completion screen with green gradient design
class SetupCompletionScreen extends HookConsumerWidget {
  const SetupCompletionScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.center,
            radius: 1.5,
            colors: [
              Color(0xFF00FF88), // Bright green center
              Color(0xFF00CC66), // Medium green
              Color(0xFF009944), // Darker green
              Color(0xFF001122), // Very dark background
            ],
            stops: [0.0, 0.3, 0.6, 1.0],
          ),
        ),
        child: Safe<PERSON><PERSON>(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                const SizedBox(height: 60),
                _buildTitle(),
                const Spacer(),
                _buildCenterCircle(),
                const Spacer(),
                _buildCompleteButton(context, ref),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Column(
      children: [
        Text(
          'Super!',
          style: AppTextStyles.tagline.copyWith(
            fontSize: 48,
            color: Colors.white,
            fontWeight: FontWeight.w300,
          ),
        ),
        Text(
          'Dein Setup',
          style: AppTextStyles.tagline.copyWith(
            fontSize: 48,
            color: Colors.white,
            fontWeight: FontWeight.w300,
          ),
        ),
        Text(
          'ist komplett.',
          style: AppTextStyles.tagline.copyWith(
            fontSize: 48,
            color: Colors.white,
            fontWeight: FontWeight.w300,
          ),
        ),
      ],
    );
  }

  Widget _buildCenterCircle() {
    return Container(
      width: 300,
      height: 300,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          center: Alignment.center,
          radius: 0.8,
          colors: [
            Colors.transparent,
            Colors.black.withValues(alpha: 0.3),
            Colors.black.withValues(alpha: 0.6),
            Colors.black.withValues(alpha: 0.9),
          ],
          stops: const [0.0, 0.4, 0.7, 1.0],
        ),
      ),
      child: Center(
        child: Text(
          'safea',
          style: AppTextStyles.tagline.copyWith(
            fontSize: 32,
            color: Colors.white,
            fontWeight: FontWeight.w300,
          ),
        ),
      ),
    );
  }

  Widget _buildCompleteButton(BuildContext context, WidgetRef ref) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
      ),
      child: AppButton(
        text: 'Bleib in Bereitschaft',
        onPressed: () => _handleComplete(context, ref),
        type: AppButtonType.primary,
        size: AppButtonSize.large,
      ),
    );
  }

  Future<void> _handleComplete(BuildContext context, WidgetRef ref) async {
    // Mark all setup steps as completed
    await Future.wait([
      ref.read(setupStatusProvider.notifier).setCompleted(true),
      ref.read(permissionsStatusProvider.notifier).setGranted(true),
      ref.read(agreementsStatusProvider.notifier).setAccepted(true),
      ref.read(timerSetupStatusProvider.notifier).setCompleted(true),
    ]);

    // Navigate to dashboard home
    if (context.mounted) {
      context.go(RouteNames.dashboardHome);
    }
  }
}
