import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/text_styles.dart';
import 'package:safea/shared/widgets/app_scaffold.dart';
import 'package:safea/shared/widgets/app_button.dart';

/// AGB (Terms & Conditions) screen with scrollable content
class Setup01AgbScreen extends StatelessWidget {
  const Setup01AgbScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: AppColors.background,
      showBackButton: true,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              Text(
                'Unsere AGB\'s',
                style: AppTextStyles.tagline.copyWith(fontSize: 28),
              ),
              const SizedBox(height: 40),
              Expanded(child: _buildAgbContent()),
              _buildActions(context),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAgbContent() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(16),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'THE EUROPEAN LANGUAGES ARE MEMBERS OF THE SAME FAMILY. THEIR SEPARATE EXISTENCE IS A MYTH. FOR SCIENCE, MUSIC, SPORT, ETC, EUROPE USES THE SAME VOCABULARY. THE LANGUAGES ONLY DIFFER IN THEIR GRAMMAR, THEIR PRONUNCIATION AND THEIR MOST COMMON WORDS. EVERYONE REALIZES WHY A NEW COMMON LANGUAGE WOULD BE DESIRABLE: ONE COULD REFUSE TO PAY EXPENSIVE TRANSLATORS. TO ACHIEVE THIS, IT WOULD BE NECESSARY TO HAVE UNIFORM GRAMMAR, PRONUNCIATION AND MORE COMMON WORDS.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.onSurfaceVariant,
                height: 1.6,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'IF SEVERAL LANGUAGES COALESCE, THE GRAMMAR OF THE RESULTING LANGUAGE IS MORE SIMPLE AND REGULAR THAN THAT OF THE INDIVIDUAL LANGUAGES. THE NEW COMMON LANGUAGE WILL BE MORE SIMPLE AND REGULAR THAN THE EXISTING EUROPEAN LANGUAGES. IT WILL BE AS SIMPLE AS OCCIDENTAL; IN FACT, IT WILL BE OCCIDENTAL.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.onSurfaceVariant,
                height: 1.6,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'THE EUROPEAN LANGUAGES ARE MEMBERS OF THE SAME FAMILY. THEIR SEPARATE EXISTENCE IS A MYTH. FOR SCIENCE, MUSIC, SPORT, ETC, EUROPE USES THE SAME VOCABULARY. THE LANGUAGES ONLY DIFFER IN THEIR GRAMMAR, THEIR PRONUNCIATION AND THEIR MOST COMMON WORDS. EVERYONE REALIZES WHY A NEW COMMON LANGUAGE WOULD BE DESIRABLE: ONE COULD REFUSE TO PAY EXPENSIVE TRANSLATORS. TO ACHIEVE THIS, IT WOULD BE NECESSARY TO HAVE UNIFORM GRAMMAR, PRONUNCIATION AND MORE COMMON WORDS.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.onSurfaceVariant,
                height: 1.6,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'IF SEVERAL LANGUAGES COALESCE, THE GRAMMAR OF THE RESULTING LANGUAGE IS MORE SIMPLE AND REGULAR THAN THAT OF THE INDIVIDUAL LANGUAGES. THE NEW COMMON LANGUAGE WILL BE MORE SIMPLE AND REGULAR THAN THE EXISTING EUROPEAN LANGUAGES. IT WILL BE AS SIMPLE AS OCCIDENTAL; IN FACT, IT WILL BE OCCIDENTAL.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.onSurfaceVariant,
                height: 1.6,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'TO AN ENGLISH PERSON, IT WILL SEEM LIKE SIMPLIFIED ENGLISH, AS A SKEPTICAL CAMBRIDGE FRIEND OF MINE TOLD ME WHAT OCCIDENTAL IS. THE EUROPEAN LANGUAGES ARE MEMBERS OF THE SAME FAMILY. THEIR SEPARATE EXISTENCE IS A MYTH. FOR SCIENCE, MUSIC, SPORT, ETC, EUROPE USES THE SAME VOCABULARY.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.onSurfaceVariant,
                height: 1.6,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'THE LANGUAGES ONLY DIFFER IN THEIR GRAMMAR, THEIR PRONUNCIATION AND THEIR MOST COMMON WORDS. EVERYONE REALIZES WHY A NEW COMMON LANGUAGE WOULD BE DESIRABLE: ONE COULD REFUSE TO PAY EXPENSIVE TRANSLATORS. TO ACHIEVE THIS, IT WOULD BE NECESSARY TO HAVE UNIFORM GRAMMAR, PRONUNCIATION AND MORE COMMON WORDS.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.onSurfaceVariant,
                height: 1.6,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'IF SEVERAL LANGUAGES COALESCE, THE GRAMMAR OF THE RESULTING LANGUAGE IS MORE SIMPLE AND REGULAR THAN THAT OF THE INDIVIDUAL LANGUAGES. THE NEW COMMON LANGUAGE WILL BE MORE SIMPLE AND REGULAR THAN THE EXISTING EUROPEAN LANGUAGES.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.onSurfaceVariant,
                height: 1.6,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActions(BuildContext context) {
    return AppButton(
      text: 'Ich habe die AGB\'s gelesen und stimme zu',
      onPressed: () => _handleAccept(context),
      type: AppButtonType.primary,
      size: AppButtonSize.large,
    );
  }

  void _handleAccept(BuildContext context) {
    // Go back to the setup01 screen
    context.pop();
  }
}
