import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/text_styles.dart';
import 'package:safea/shared/widgets/app_scaffold.dart';
import 'package:safea/shared/widgets/app_button.dart';

/// Tracking setup screen with German design
class SetupTrackingScreen extends ConsumerWidget {
  const SetupTrackingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppScaffold(
      backgroundColor: AppColors.background,
      showBackButton: true,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              Text(
                'Tracking einschalten',
                style: AppTextStyles.tagline.copyWith(fontSize: 28),
              ),
              const SizedBox(height: 40),
              Expanded(child: _buildTrackingBenefits()),
              _buildActions(context),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrackingBenefits() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildBenefitItem(
          'Durch dein persönliches Tracking können wir dich benachrichtigen, wenn in deiner Nähe jemand Unterstützung braucht – so kannst du schnell helfen.',
          true,
        ),
        const SizedBox(height: 32),
        _buildBenefitItem(
          'Deine Daten bleiben sicher. Wir verkaufen deine Daten nicht und geben sie nicht weiter. Wir engagieren uns ethisch, um mit dir die Welt sicherer zu machen.',
          true,
        ),
        const Spacer(),
        _buildBottomText(),
      ],
    );
  }

  Widget _buildBenefitItem(String text, bool isEnabled) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 20,
          height: 20,
          margin: const EdgeInsets.only(top: 2),
          decoration: BoxDecoration(
            color: isEnabled ? AppColors.success : AppColors.border,
            borderRadius: BorderRadius.circular(4),
          ),
          child: isEnabled
              ? const Icon(Icons.check, color: Colors.white, size: 14)
              : null,
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            text,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.onBackground,
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomText() {
    return Text(
      'Tracking erlauben und aktivieren – gemeinsam ethisch sinnvoll helfen und unterstützen.',
      style: AppTextStyles.bodyMedium.copyWith(
        color: AppColors.onBackgroundSecondary,
        height: 1.4,
      ),
    );
  }

  Widget _buildActions(BuildContext context) {
    return AppButton(
      text: 'Tracking einschalten',
      onPressed: () => _handleEnableTracking(context),
      type: AppButtonType.primary,
      size: AppButtonSize.large,
    );
  }

  void _handleEnableTracking(BuildContext context) {
    // Navigate to available times setup
    context.push(RouteNames.setupAvailableTimes);
  }
}
