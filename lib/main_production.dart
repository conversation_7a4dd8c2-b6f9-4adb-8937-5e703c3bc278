import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:safea/core/constants/flavor_config.dart';
import 'package:safea/core/router/app_router.dart';
import 'package:safea/core/storage/storage_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize flavor configuration for production
  FlavorConfig.initialize(FlavorConfig.production());

  // Initialize storage service
  await StorageService.initialize();

  runApp(const ProviderScope(child: SafeaApp()));
}

class SafeaApp extends ConsumerWidget {
  const SafeaApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(appRouterProvider);
    final config = FlavorConfig.instance;

    return MaterialApp.router(
      title: config.name,
      routerConfig: router,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      debugShowCheckedModeBanner: config.isDevelopment,
    );
  }
}
