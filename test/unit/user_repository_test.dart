import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:safea/features/user/data/datasources/user_remote_datasource.dart';
import 'package:safea/features/user/data/models/user_model.dart';
import 'package:safea/features/user/data/repositories/user_repository_impl.dart';
import 'package:safea/features/user/domain/entities/user.dart';

class MockUserRemoteDataSource extends Mo<PERSON> implements UserRemoteDataSource {}

void main() {
  late UserRepositoryImpl repository;
  late MockUserRemoteDataSource mockRemoteDataSource;

  setUp(() {
    mockRemoteDataSource = MockUserRemoteDataSource();
    repository = UserRepositoryImpl(mockRemoteDataSource);
  });

  group('UserRepository', () {
    const tUserModel = UserModel(
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '************',
      website: 'johndoe.com',
      address: AddressModel(
        street: 'Kulas Light',
        suite: 'Apt. 556',
        city: 'Gwenborough',
        zipcode: '92998-3874',
        geo: GeoModel(lat: '-37.3159', lng: '81.1496'),
      ),
      company: CompanyModel(
        name: 'Romaguera-Crona',
        catchPhrase: 'Multi-layered client-server neural-net',
        bs: 'harness real-time e-markets',
      ),
    );

    const tUser = User(
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '************',
      website: 'johndoe.com',
      address: Address(
        street: 'Kulas Light',
        suite: 'Apt. 556',
        city: 'Gwenborough',
        zipcode: '92998-3874',
        geo: Geo(lat: '-37.3159', lng: '81.1496'),
      ),
      company: Company(
        name: 'Romaguera-Crona',
        catchPhrase: 'Multi-layered client-server neural-net',
        bs: 'harness real-time e-markets',
      ),
    );

    test('should return list of users when getUsers is called', () async {
      // arrange
      when(() => mockRemoteDataSource.getUsers())
          .thenAnswer((_) async => [tUserModel]);

      // act
      final result = await repository.getUsers();

      // assert
      expect(result, [tUser]);
      verify(() => mockRemoteDataSource.getUsers()).called(1);
    });

    test('should return user when getUserById is called', () async {
      // arrange
      when(() => mockRemoteDataSource.getUserById(1))
          .thenAnswer((_) async => tUserModel);

      // act
      final result = await repository.getUserById(1);

      // assert
      expect(result, tUser);
      verify(() => mockRemoteDataSource.getUserById(1)).called(1);
    });
  });
}
