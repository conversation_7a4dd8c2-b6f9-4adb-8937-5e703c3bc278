import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:safea/features/user/domain/entities/user.dart';
import 'package:safea/features/user/presentation/pages/user_list_page.dart';
import 'package:safea/features/user/presentation/providers/user_list_provider.dart';

class MockUserListNotifier extends Mock implements UserListNotifier {}

void main() {
  late MockUserListNotifier mockNotifier;

  setUp(() {
    mockNotifier = MockUserListNotifier();
  });

  const tUsers = [
    User(
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '************',
      website: 'johndoe.com',
      address: Address(
        street: 'Kulas Light',
        suite: 'Apt. 556',
        city: 'Gwenborough',
        zipcode: '92998-3874',
        geo: Geo(lat: '-37.3159', lng: '81.1496'),
      ),
      company: Company(
        name: 'Romaguera-Crona',
        catchPhrase: 'Multi-layered client-server neural-net',
        bs: 'harness real-time e-markets',
      ),
    ),
  ];

  Widget createWidgetUnderTest() {
    return ProviderScope(
      overrides: [
        userListNotifierProvider.overrideWith(() => mockNotifier),
      ],
      child: const MaterialApp(
        home: UserListPage(),
      ),
    );
  }

  group('UserListPage', () {
    testWidgets('should show loading indicator when state is loading',
        (WidgetTester tester) async {
      // arrange
      when(() => mockNotifier.build())
          .thenAnswer((_) async => throw Exception('Loading'));

      // act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump();

      // assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should show users when state has data',
        (WidgetTester tester) async {
      // arrange
      when(() => mockNotifier.build()).thenAnswer((_) async => tUsers);

      // act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // assert
      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
    });

    testWidgets('should show error message when state has error',
        (WidgetTester tester) async {
      // arrange
      when(() => mockNotifier.build())
          .thenThrow(Exception('Failed to load users'));

      // act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // assert
      expect(find.text('Error loading users'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });
  });
}
