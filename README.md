# Safea

Eine Flutter-App für Sicherheit und Unterstützung mit modernen Entwicklungspraktiken.

## 🚀 Features

### ✅ Enabled Features
- **German Localization** as default language with fallback to English
- **Complete Setup Flow** with agreements, tracking, and time configuration
- **Dashboard with Swipe Navigation** between home, tracking, and done screens
- **Dio HTTP client** for networking with interceptors and error handling
- **GoRouter** for declarative routing
- **Flavor configuration** (development, staging, production)
- **Repository pattern** for API communication
- **Feature-first folder structure** for better organization
- **Riverpod + Riverpod Hooks** for state management
- **MVVM with Clean Architecture** for scalable code organization
- **Code generation** (Freezed, JsonSerializable, Riverpod)
- **Permission handling** for location and notifications
- **Local storage** with SharedPreferences

### ❌ Disabled Features
- Firebase
- Supabase
- AutoRoute

## 🏗️ Architecture

This project follows **Clean Architecture** principles with **MVVM** pattern:

```
lib/
├── core/                          # Core functionality
│   ├── constants/                 # App constants and flavor configuration
│   ├── router/                    # App routing configuration
│   ├── storage/                   # Local storage services and providers
│   └── theme/                     # App theming (colors, text styles)
├── features/                      # Feature modules
│   ├── auth/                      # Authentication (login, signup, verification)
│   ├── dashboard/                 # Dashboard screens with swipe navigation
│   ├── settings/                  # App settings
│   └── setup/                     # Setup flow (agreements, tracking, times)
├── l10n/                          # Localization files (German default)
├── shared/                        # Shared widgets and utilities
│   └── widgets/                   # Reusable UI components
└── main.dart                      # App entry point (development flavor)
```

### Layers

1. **Domain Layer**: Contains business logic, entities, and repository interfaces
2. **Data Layer**: Implements repositories, handles API calls, and data models
3. **Presentation Layer**: UI components, pages, and state management

## 🎯 App Flow

### Authentication Flow
1. **Splash Screen** → **Start Screen** → **Login/Signup** → **Verification**

### Setup Flow
2. **Verification** → **Setup01 (Agreements)** → **Setup Start (120s)** → **Tracking** → **Available Times** → **Setup Done** → **Dashboard**

### Dashboard Flow
3. **Dashboard Home** ↔ **User Tracking** ↔ **User Done** (swipe navigation)

## 🎯 Flavors

The project supports three flavors for different environments:

| Flavor | Description | App Name | Base URL |
|--------|-------------|----------|----------|
| **development** | Local development with debug features | Safea (Dev) | `https://dev-api.safea.com` |
| **staging** | Testing environment | Safea (Staging) | `https://staging-api.safea.com` |
| **production** | Production release | Safea | `https://api.safea.com` |

### Running Different Flavors

```bash
# Development (default)
flutter run --flavor development --target lib/main_development.dart

# Staging
flutter run --flavor staging --target lib/main_staging.dart

# Production
flutter run --flavor production --target lib/main_production.dart
```

## 🚀 Getting Started

### Prerequisites

- Flutter SDK (>=3.8.1)
- Dart SDK (>=3.8.1)
- IDE with Flutter support (VS Code, Android Studio, IntelliJ)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd safea
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code**
   ```bash
   flutter packages pub run build_runner build --delete-conflicting-outputs
   ```

4. **Run the app**
   ```bash
   # Development flavor (default)
   flutter run

   # Or specify flavor explicitly
   flutter run --flavor development --target lib/main_development.dart
   ```

## 🌍 Localization

The app is configured with **German as the default language** with English as fallback.

### Supported Languages
- 🇩🇪 **German (Deutsch)** - Default
- 🇬🇧 **English** - Fallback

### Adding New Translations
1. Add new strings to `lib/l10n/app_de.arb`
2. Run `flutter gen-l10n` to generate localization files
3. Use `AppLocalizations.of(context).yourString` in widgets

## 🌐 Backend Integration

This project is configured to communicate with a **custom backend** using REST API.

### API Configuration

- Base URLs are configured per flavor in `FlavorConfig`
- Uses Dio with interceptors for logging and error handling
- Implements a reusable `ApiClient` service
- Currently uses mock data for MVP development

## 🧪 Testing

The project includes comprehensive testing setup:

### Test Structure
```
test/
├── unit/                          # Unit tests
├── widget/                        # Widget tests
└── integration/                   # Integration tests
```

### Running Tests

```bash
# Run all tests
flutter test

# Run specific test files
flutter test test/unit/user_repository_test.dart
flutter test test/widget/user_list_page_test.dart

# Run tests with coverage
flutter test --coverage
```

### Test Examples

- **Unit Tests**: Repository pattern, use cases, business logic
- **Widget Tests**: UI components, user interactions, state changes
- **Integration Tests**: Complete user flows (to be added)

## 🛠️ Development Workflow

### Code Generation

Run code generation after making changes to:
- Freezed classes
- JSON serializable models
- Riverpod providers

```bash
# One-time generation
flutter packages pub run build_runner build --delete-conflicting-outputs

# Watch for changes
flutter packages pub run build_runner watch --delete-conflicting-outputs
```

### Adding New Features

1. Create feature structure under `lib/features/your_feature/`
2. Follow the three-layer architecture (domain, data, presentation)
3. Use the User feature as a reference
4. Add tests for each layer
5. Update routing if needed

## 📱 Platform Support

- ✅ **Android** (API 21+)
- ✅ **iOS** (iOS 12+)
- ❌ **Web** (not configured)
- ❌ **Desktop** (not configured)

## 🔧 Configuration

### Flavor Configuration

Each flavor has its own configuration in `lib/core/constants/flavor_config.dart`:

```dart
// Development
FlavorConfig.development()
  - baseUrl: 'https://dev-api.safea.com'
  - enableLogging: true
  - debugShowCheckedModeBanner: true

// Staging
FlavorConfig.staging()
  - baseUrl: 'https://staging-api.safea.com'
  - enableLogging: true
  - debugShowCheckedModeBanner: false

// Production
FlavorConfig.production()
  - baseUrl: 'https://api.safea.com'
  - enableLogging: false
  - debugShowCheckedModeBanner: false
```

### Network Configuration

The `ApiClient` is configured with:
- 30-second timeouts for connect/receive/send
- Pretty logging in development/staging
- Custom error handling and exceptions
- Automatic JSON content-type headers

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and ensure they pass
6. Submit a pull request

## 🎨 UI Features

### Design System
- **Custom color scheme** with green gradient themes
- **Consistent text styles** across the app
- **Reusable UI components** (AppButton, AppScaffold, etc.)
- **Material Design 3** with custom theming

### Key Screens
- **Setup Flow**: Agreement → AGB → 120s Timer → Tracking → Available Times → Done
- **Dashboard**: Home with swipe navigation to tracking and done screens
- **Authentication**: Login, signup, and verification screens

## 🧪 Testing & Development

### For UI Testing
- Storage is commented out for development to see setup screens on every run
- Use iOS simulator for testing (recommended over web)
- MVP approach with mock data for all API calls

### Running the App
```bash
# Development (default)
flutter run

# With specific device
flutter run -d "iPhone 15 Pro"
```

## 📞 Support

For questions and support, please check the project documentation or create an issue in the repository.
